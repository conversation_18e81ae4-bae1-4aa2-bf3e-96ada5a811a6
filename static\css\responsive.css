/* T-Office Responsive Design - Mobile First Approach */

/* Base Mobile Styles (320px and up) */
@media (min-width: 320px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .modern-nav .navbar-brand {
        font-size: 1.25rem;
    }
    
    .brand-text {
        display: none;
    }
    
    .search-container {
        width: 100%;
        margin: 1rem 0;
        order: 3;
    }
    
    .navbar-nav {
        width: 100%;
    }
    
    .nav-link {
        padding: 0.75rem 1rem !important;
        border-radius: 0 !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .user-menu {
        justify-content: center;
    }
    
    .main-content {
        padding-top: 0.5rem;
    }
    
    /* Dashboard Mobile */
    .dashboard-header {
        padding: 1.5rem;
        text-align: center;
    }
    
    .welcome-text {
        font-size: 1.75rem;
    }
    
    .quick-actions {
        justify-content: center;
        margin-top: 1rem;
    }
    
    .quick-action-btn {
        flex: 1;
        max-width: 200px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .stats-card {
        padding: 1.5rem 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    /* File Cards Mobile */
    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;
    }
    
    .file-actions {
        align-self: stretch;
        justify-content: space-around;
    }
    
    /* Scanner Mobile */
    .scanner-container {
        margin: 0;
    }
    
    .camera-container {
        height: 250px;
    }
    
    .scanner-overlay {
        width: 180px;
        height: 180px;
    }
    
    .scanner-controls {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .control-btn {
        width: 100%;
        justify-content: center;
    }
    
    /* Form Mobile */
    .form-body {
        padding: 1rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .location-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .upload-zone {
        padding: 2rem 1rem;
    }
    
    /* Analytics Mobile */
    .insights-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .insight-card {
        padding: 1.5rem 1rem;
    }
    
    .insight-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .insight-value {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .chart-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .chart-controls {
        justify-content: center;
    }
    
    /* Tables Mobile */
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .modern-table th,
    .modern-table td {
        padding: 0.75rem 0.5rem;
    }
}

/* Small Mobile (375px and up) */
@media (min-width: 375px) {
    .brand-text {
        display: inline;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .insights-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .camera-container {
        height: 300px;
    }
    
    .scanner-overlay {
        width: 200px;
        height: 200px;
    }
}

/* Large Mobile (425px and up) */
@media (min-width: 425px) {
    .container-fluid {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
    
    .welcome-text {
        font-size: 2rem;
    }
    
    .quick-action-btn {
        flex: none;
    }
    
    .scanner-controls {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .control-btn {
        width: auto;
        min-width: 150px;
    }
    
    .upload-zone {
        padding: 2.5rem 1.5rem;
    }
}

/* Tablet Portrait (768px and up) */
@media (min-width: 768px) {
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
    
    .main-content {
        padding-top: 0.5rem;
    }
    
    .search-container {
        width: 300px;
        margin: 0;
        order: initial;
    }
    
    .navbar-nav {
        width: auto;
    }
    
    .nav-link {
        border-bottom: none;
    }
    
    .dashboard-header {
        padding: 2rem;
        text-align: left;
    }
    
    .welcome-text {
        font-size: 2.5rem;
    }
    
    .quick-actions {
        justify-content: flex-end;
        margin-top: 0;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .dashboard-grid {
        grid-template-columns: 2fr 1fr;
    }
    
    .file-item {
        flex-direction: row;
        align-items: center;
        gap: 0;
    }
    
    .file-actions {
        align-self: auto;
        justify-content: flex-end;
    }
    
    .camera-container {
        height: 400px;
    }
    
    .scanner-overlay {
        width: 250px;
        height: 250px;
    }
    
    .form-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .location-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .insights-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .chart-container {
        height: 350px;
    }
    
    .chart-header {
        flex-direction: row;
        text-align: left;
    }
}

/* Desktop (1200px and up) */
@media (min-width: 1200px) {
    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
        padding-left: 2rem;
        padding-right: 2rem;
    }
    
    .welcome-text {
        font-size: 3rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 2.5fr 1fr;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .hover-lift:hover {
        transform: none;
    }
    
    .action-btn:hover {
        transform: none;
    }
    
    .modern-card:hover {
        transform: none;
    }
    
    /* Larger touch targets */
    .action-btn {
        min-width: 44px;
        min-height: 44px;
    }
    
    .nav-link {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
    
    .chart-btn {
        min-height: 44px;
        padding: 0.75rem 1.5rem;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .voice-indicator {
        animation: none !important;
    }
    
    .search-btn.listening {
        animation: none !important;
    }
}
