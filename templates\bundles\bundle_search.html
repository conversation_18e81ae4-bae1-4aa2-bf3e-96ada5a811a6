<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Bundle {{ bundle.bundle_number }} - T-Office</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .search-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }
        
        .search-form {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .results-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .file-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .file-item:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .file-item.bulk-data {
            border-left: 4px solid #ffc107;
            background: linear-gradient(135deg, #fff 0%, #fffbf0 100%);
        }
        
        .file-title {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 0.5rem;
        }
        
        .file-meta {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        
        .file-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .action-btn {
            padding: 0.25rem 0.75rem;
            border: none;
            border-radius: 4px;
            font-size: 0.8rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .action-btn.primary {
            background: #007bff;
            color: white;
        }
        
        .action-btn.primary:hover {
            background: #0056b3;
            color: white;
        }
        
        .action-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .action-btn.warning:hover {
            background: #e0a800;
            color: #212529;
        }
        
        .action-btn.secondary {
            background: #6c757d;
            color: white;
        }
        
        .action-btn.secondary:hover {
            background: #545b62;
            color: white;
        }
        
        .bulk-data-indicator {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: bold;
            margin-left: 0.5rem;
        }
        
        .search-stats {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .no-results {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .no-results i {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .session-status {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: #155724;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .search-tips {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .breadcrumb-nav {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 1px 5px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('bundle_detail', bundle_number=bundle.bundle_number) }}">
                <i class="fas fa-search me-2"></i>Search Bundle {{ bundle.bundle_number }}
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('bundle_detail', bundle_number=bundle.bundle_number) }}">
                    <i class="fas fa-arrow-left me-1"></i>Back to Bundle
                </a>
                <a class="nav-link" href="{{ url_for('bundle_list') }}">
                    <i class="fas fa-archive me-1"></i>All Bundles
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Breadcrumb Navigation -->
        <div class="breadcrumb-nav">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('bundle_list') }}">
                            <i class="fas fa-archive me-1"></i>Bundles
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('bundle_detail', bundle_number=bundle.bundle_number) }}">
                            Bundle {{ bundle.bundle_number }}
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Search</li>
                </ol>
            </nav>
        </div>

        <!-- Search Header -->
        <div class="search-header">
            <h1>
                <i class="fas fa-search me-2"></i>
                Search Bundle {{ bundle.bundle_number }}
            </h1>
            <p class="mb-0">
                Search through {{ bundle.current_count }} files in this bundle
            </p>
        </div>

        <!-- Session Status -->
        {% if active_session %}
        <div class="session-status">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Bulk Data Access Active</strong> - {{ active_session.time_remaining() }} minutes remaining
                </div>
                <div>
                    <button class="btn btn-sm btn-outline-success me-2" onclick="extendSession()">
                        <i class="fas fa-clock me-1"></i>Extend
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="revokeSession()">
                        <i class="fas fa-times me-1"></i>End
                    </button>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Search Form -->
        <div class="search-form">
            <form method="GET" action="{{ url_for('bundle_search', bundle_number=bundle.bundle_number) }}">
                <div class="row">
                    <div class="col-md-10">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control form-control-lg" name="q" 
                                   placeholder="Search by title, RefID, file number, subject, location..." 
                                   value="{{ search_query }}" autofocus>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary btn-lg w-100" type="submit">
                            <i class="fas fa-search me-1"></i>Search
                        </button>
                    </div>
                </div>
            </form>
            
            <div class="search-tips">
                <h6><i class="fas fa-lightbulb me-1"></i>Search Tips:</h6>
                <ul class="mb-0">
                    <li>Search across all file attributes including RefID, FILE_NO, Subject, and location data</li>
                    <li>Use partial matches - searching "2023" will find all files from 2023</li>
                    <li>Search is case-insensitive</li>
                    <li>Leave empty to show all files in the bundle</li>
                </ul>
            </div>
        </div>

        <!-- Search Results -->
        <div class="results-section">
            {% if search_query %}
            <div class="search-stats">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>{{ files|length }}</strong> files found for 
                        <span class="badge bg-primary">"{{ search_query }}"</span>
                    </div>
                    <div>
                        <a href="{{ url_for('bundle_search', bundle_number=bundle.bundle_number) }}" 
                           class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Clear Search
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4>
                    <i class="fas fa-file-alt me-2"></i>
                    {% if search_query %}
                    Search Results ({{ files|length }})
                    {% else %}
                    All Files in Bundle ({{ files|length }})
                    {% endif %}
                </h4>
            </div>

            {% if files %}
                {% for result in files %}
                <div class="file-item {% if result.is_bulk_data %}bulk-data{% endif %}">
                    {% if result.is_bulk_data %}
                    <div class="bulk-data-indicator">
                        <i class="fas fa-shield-alt"></i>
                        Protected Data
                        {% if result.requires_verification %}
                        <span class="verification-required">Verification Required</span>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <div class="file-title">
                        {{ result.file.title }}
                        {% if result.is_bulk_data and not active_session %}
                        <i class="fas fa-lock text-warning ms-2" title="Requires verification"></i>
                        {% endif %}
                    </div>
                    
                    <div class="file-meta">
                        <strong>File ID:</strong> {{ result.file.id }} |
                        {% if result.file.excel_row_data %}
                        {% set excel_data = result.file.excel_row_data | from_json %}
                        {% if excel_data.RefID %}
                        <strong>RefID:</strong> {{ excel_data.RefID }} |
                        {% endif %}
                        {% if excel_data.FILE_NO %}
                        <strong>File No:</strong> {{ excel_data.FILE_NO }} |
                        {% endif %}
                        {% if excel_data.Subject %}
                        <strong>Subject:</strong> {{ excel_data.Subject[:50] }}{% if excel_data.Subject|length > 50 %}...{% endif %} |
                        {% endif %}
                        {% if excel_data.hobli_name %}
                        <strong>Location:</strong> {{ excel_data.hobli_name }}, {{ excel_data.village_name }} |
                        {% endif %}
                        {% endif %}
                        <strong>Created:</strong> {{ result.file.created_at.strftime('%Y-%m-%d') }}
                    </div>
                    
                    <div class="file-actions">
                        {% if result.is_bulk_data and result.requires_verification and not active_session %}
                        <a href="{{ url_for('bulk_data_unlock') }}" class="action-btn warning">
                            <i class="fas fa-unlock"></i>Verify Access
                        </a>
                        {% else %}
                        <a href="{{ url_for('view_file', file_id=result.file.id) }}" class="action-btn primary">
                            <i class="fas fa-eye"></i>View Details
                        </a>
                        {% endif %}
                        {% if result.file.qr_code %}
                        <a href="{{ url_for('get_qrcode', file_id=result.file.id) }}" class="action-btn secondary" target="_blank">
                            <i class="fas fa-qrcode"></i>QR Code
                        </a>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    {% if search_query %}
                    <h5>No Results Found</h5>
                    <p>No files found matching "<strong>{{ search_query }}</strong>" in Bundle {{ bundle.bundle_number }}.</p>
                    <p>Try:</p>
                    <ul class="list-unstyled">
                        <li>• Using different search terms</li>
                        <li>• Checking spelling</li>
                        <li>• Using partial matches</li>
                        <li>• Searching for RefID or file numbers</li>
                    </ul>
                    {% else %}
                    <h5>Bundle is Empty</h5>
                    <p>Bundle {{ bundle.bundle_number }} currently contains no files.</p>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Session management functions
        {% if active_session %}
        function extendSession() {
            fetch('/bulk-data-session/extend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to extend session: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error extending session:', error);
                alert('Error extending session');
            });
        }

        function revokeSession() {
            if (confirm('Are you sure you want to end your current verification session?')) {
                fetch('/bulk-data-session/revoke', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Failed to revoke session: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error revoking session:', error);
                    alert('Error revoking session');
                });
            }
        }
        {% endif %}

        // Auto-focus search input
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('input[name="q"]');
            if (searchInput && !searchInput.value) {
                searchInput.focus();
            }
        });
    </script>
</body>
</html>
