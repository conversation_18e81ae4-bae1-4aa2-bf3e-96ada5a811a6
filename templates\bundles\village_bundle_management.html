{% extends "base.html" %}

{% block title %}Village-Based Bundle Management - Taluk Office{% endblock %}

{% block content %}
<style>
    /* Sophisticated Village-Based Bundle Management Styles */
    .bundle-management-header {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 3rem;
        border-radius: 0 0 30px 30px;
        position: relative;
        overflow: hidden;
    }

    .bundle-management-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .bundle-management-header .container {
        position: relative;
        z-index: 1;
    }

    .bundle-management-header h1 {
        font-size: 3rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .bundle-management-header .subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-top: 0.5rem;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: white;
        border-radius: 25px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .stat-card .stat-number {
        font-size: 3rem;
        font-weight: 900;
        color: #1e3a8a;
        margin: 0;
        line-height: 1;
    }

    .stat-card .stat-label {
        color: #6b7280;
        font-size: 1rem;
        font-weight: 600;
        margin-top: 0.5rem;
    }

    .stat-card .stat-icon {
        font-size: 2.5rem;
        color: #3b82f6;
        opacity: 0.8;
        float: right;
        margin-top: -3rem;
    }

    .village-sections {
        margin-bottom: 3rem;
    }

    .village-section {
        background: white;
        border-radius: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 2rem;
        border: 1px solid rgba(59, 130, 246, 0.1);
    }

    .village-header {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        color: white;
        padding: 2rem;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .village-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .village-header:hover::before {
        transform: translateX(100%);
    }

    .village-title {
        font-size: 2rem;
        font-weight: 800;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .village-toggle {
        font-size: 1.5rem;
        transition: transform 0.3s ease;
        opacity: 0.8;
    }

    .village-section.collapsed .village-toggle {
        transform: rotate(-90deg);
    }

    .village-section.collapsed .racks-container {
        display: none;
    }

    .village-stats {
        display: flex;
        gap: 2rem;
        margin-top: 1rem;
    }

    .village-stat {
        text-align: center;
    }

    .village-stat-number {
        font-size: 1.5rem;
        font-weight: 800;
        margin: 0;
    }

    .village-stat-label {
        font-size: 0.85rem;
        opacity: 0.8;
        margin: 0;
    }

    .racks-container {
        padding: 2rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .rack-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    .rack-card {
        background: white;
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .rack-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        border-color: #3b82f6;
    }

    .rack-header-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .rack-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #1e3a8a;
        margin: 0;
    }

    .rack-stats-mini {
        display: flex;
        gap: 1rem;
        font-size: 0.85rem;
        color: #6b7280;
    }

    .bundles-in-rack {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .bundle-mini-card {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: 2px solid #e2e8f0;
        border-radius: 15px;
        padding: 1rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .bundle-mini-card:hover {
        transform: translateY(-3px);
        border-color: #3b82f6;
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    }

    .bundle-mini-number {
        font-size: 1.2rem;
        font-weight: 800;
        color: #1e3a8a;
        margin-bottom: 0.5rem;
    }

    .bundle-mini-count {
        font-size: 0.8rem;
        color: #6b7280;
        font-weight: 600;
    }

    .search-filters {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(59, 130, 246, 0.1);
    }

    .search-filters .form-control {
        border-radius: 15px;
        border: 2px solid #e5e7eb;
        padding: 1rem 1.25rem;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .search-filters .form-control:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        transform: translateY(-2px);
    }

    .empty-state {
        text-align: center;
        padding: 4rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 5rem;
        margin-bottom: 1.5rem;
        opacity: 0.3;
        color: #3b82f6;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .rack-cards {
            grid-template-columns: 1fr;
        }

        .bundles-in-rack {
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        }

        .bundle-management-header h1 {
            font-size: 2.2rem;
        }

        .stats-cards {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
    }
</style>

<!-- Bundle Management Header -->
<div class="bundle-management-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-map-marked-alt me-3"></i>Village-Based Bundle Management</h1>
            <p class="subtitle">Dynamic organization by villages with rack-based storage system</p>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-number">{{ total_villages }}</div>
            <div class="stat-label">Villages</div>
            <div class="stat-icon"><i class="fas fa-map-marker-alt"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_racks }}</div>
            <div class="stat-label">Active Racks</div>
            <div class="stat-icon"><i class="fas fa-warehouse"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_bundles }}</div>
            <div class="stat-label">Total Bundles</div>
            <div class="stat-icon"><i class="fas fa-folder"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_files }}</div>
            <div class="stat-label">Total Files</div>
            <div class="stat-icon"><i class="fas fa-file-alt"></i></div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <div class="row">
            <div class="col-md-6">
                <input type="text" class="form-control" id="villageSearch" placeholder="🔍 Search villages..." onkeyup="filterVillages()">
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control" id="rackSearch" placeholder="🏢 Search racks..." onkeyup="filterRacks()">
            </div>
        </div>
    </div>

    <!-- Village Sections -->
    <div class="village-sections">
        {% if village_bundles %}
            {% for village_name, racks in village_bundles.items() %}
            <div class="village-section" data-village="{{ village_name }}">
                <div class="village-header" onclick="toggleVillage('{{ village_name }}')">
                    <div>
                        <div class="village-title">
                            <i class="fas fa-chevron-down village-toggle"></i>
                            <i class="fas fa-map-marker-alt me-2"></i>
                            {{ village_name }}
                        </div>
                        
                        <div class="village-stats">
                            <div class="village-stat">
                                <div class="village-stat-number">{{ racks|length }}</div>
                                <div class="village-stat-label">Racks</div>
                            </div>
                            <div class="village-stat">
                                {% set total_bundles_in_village = racks.values() | map('length') | sum %}
                                <div class="village-stat-number">{{ total_bundles_in_village }}</div>
                                <div class="village-stat-label">Bundles</div>
                            </div>
                            <div class="village-stat">
                                {% set total_files_in_village = 0 %}
                                {% for rack_bundles in racks.values() %}
                                    {% for bundle in rack_bundles.values() %}
                                        {% set total_files_in_village = total_files_in_village + bundle.file_count %}
                                    {% endfor %}
                                {% endfor %}
                                <div class="village-stat-number">{{ total_files_in_village }}</div>
                                <div class="village-stat-label">Files</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="racks-container">
                    <div class="rack-cards">
                        {% for rack_no, bundles in racks.items() %}
                        <div class="rack-card" data-rack="{{ rack_no }}">
                            <div class="rack-header-info">
                                <div class="rack-title">
                                    <i class="fas fa-warehouse me-2"></i>Rack {{ rack_no }}
                                </div>
                                <div class="rack-stats-mini">
                                    <span><i class="fas fa-folder me-1"></i>{{ bundles|length }} bundles</span>
                                    {% set rack_files = bundles.values() | map(attribute='file_count') | sum %}
                                    <span><i class="fas fa-file me-1"></i>{{ rack_files }} files</span>
                                </div>
                            </div>

                            <div class="bundles-in-rack">
                                {% for bundle_no, bundle_data in bundles.items() %}
                                <div class="bundle-mini-card" onclick="viewVillageBundle('{{ village_name }}', '{{ rack_no }}', '{{ bundle_no }}')">
                                    <div class="bundle-mini-number">{{ bundle_no }}</div>
                                    <div class="bundle-mini-count">{{ bundle_data.file_count }} files</div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <i class="fas fa-map-marked-alt"></i>
                <h3>No Village Data Found</h3>
                <p>Upload Excel files with village information to see village-based bundle organization.</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
    // Village toggle functionality
    function toggleVillage(villageName) {
        const villageSection = document.querySelector(`[data-village="${villageName}"]`);
        villageSection.classList.toggle('collapsed');
        
        // Save collapsed state to localStorage
        const collapsedVillages = JSON.parse(localStorage.getItem('collapsedVillages') || '[]');
        if (villageSection.classList.contains('collapsed')) {
            if (!collapsedVillages.includes(villageName)) {
                collapsedVillages.push(villageName);
            }
        } else {
            const index = collapsedVillages.indexOf(villageName);
            if (index > -1) {
                collapsedVillages.splice(index, 1);
            }
        }
        localStorage.setItem('collapsedVillages', JSON.stringify(collapsedVillages));
    }
    
    // Bundle view functionality
    function viewVillageBundle(village, rack, bundle) {
        // Navigate to village bundle detail page
        window.location.href = `/bundles/village/${encodeURIComponent(village)}/rack/${rack}/bundle/${bundle}`;
    }
    
    // Filter villages
    function filterVillages() {
        const searchTerm = document.getElementById('villageSearch').value.toLowerCase();
        const villageSections = document.querySelectorAll('.village-section');
        
        villageSections.forEach(section => {
            const villageName = section.dataset.village.toLowerCase();
            if (villageName.includes(searchTerm)) {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        });
    }
    
    // Filter racks
    function filterRacks() {
        const searchTerm = document.getElementById('rackSearch').value.toLowerCase();
        const rackCards = document.querySelectorAll('.rack-card');
        
        rackCards.forEach(card => {
            const rackNo = card.dataset.rack.toLowerCase();
            if (rackNo.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    // Restore collapsed state on page load
    document.addEventListener('DOMContentLoaded', function() {
        const collapsedVillages = JSON.parse(localStorage.getItem('collapsedVillages') || '[]');
        collapsedVillages.forEach(villageName => {
            const villageSection = document.querySelector(`[data-village="${villageName}"]`);
            if (villageSection) {
                villageSection.classList.add('collapsed');
            }
        });
    });
</script>
{% endblock %}
