{% extends "base.html" %}

{% block title %}Bundle {{ bundle_number }} Search{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Government Header (shown when accessed via QR scan) -->
    {% if from_qr %}
    <div class="government-header">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/55/Emblem_of_India.svg/200px-Emblem_of_India.svg.png" 
                     alt="Government of India" class="govt-emblem">
            </div>
            <div class="col-md-8 text-center">
                <h2 class="govt-title">Government of India</h2>
                <h3 class="dept-title">Taluk Office Document Management System</h3>
                <p class="dept-subtitle">Chikkamagaluru District, Karnataka</p>
            </div>
            <div class="col-md-2 text-center">
                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/bd/Karnataka_logo.png/150px-Karnataka_logo.png" 
                     alt="Karnataka Government" class="state-emblem">
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Bundle Information Header -->
    <div class="bundle-info-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="bundle-title">
                    <i class="fas fa-layer-group me-3"></i>Bundle {{ bundle_number }} Search
                </h1>
                <div class="bundle-location-info">
                    <span class="location-badge compartment">
                        <i class="fas fa-building me-1"></i>Compartment {{ bundle_stats.compartment }}
                    </span>
                    <span class="location-badge rack">
                        <i class="fas fa-archive me-1"></i>Rack {{ bundle_stats.rack_number }}
                    </span>
                    <span class="location-badge files">
                        <i class="fas fa-file-alt me-1"></i>{{ bundle_stats.total_files }} Files
                    </span>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Search Form -->
    <div class="search-form-container">
        <div class="search-instructions">
            <h4><i class="fas fa-search me-2"></i>Search Files in Bundle {{ bundle_number }}</h4>
            <p>Enter survey number, RefID, file number, village name, or any other details to find files in this bundle.</p>
        </div>

        <form method="GET" action="{{ url_for('bundle_search_interface', bundle_number=bundle_number) }}">
            <div class="row g-3">
                <div class="col-md-6">
                    <label for="search_query" class="form-label">Search Term</label>
                    <input type="text" id="search_query" name="q" class="form-control form-control-lg" 
                           placeholder="Enter survey number, RefID, file number, village name..." 
                           value="{{ search_query or '' }}" autofocus>
                </div>
                <div class="col-md-4">
                    <label for="search_type" class="form-label">Search Type</label>
                    <select id="search_type" name="search_type" class="form-select form-select-lg">
                        <option value="all" {% if search_type == 'all' %}selected{% endif %}>All Fields</option>
                        <option value="survey" {% if search_type == 'survey' %}selected{% endif %}>Survey Number</option>
                        <option value="ref_id" {% if search_type == 'ref_id' %}selected{% endif %}>RefID</option>
                        <option value="file_no" {% if search_type == 'file_no' %}selected{% endif %}>File Number</option>
                        <option value="village" {% if search_type == 'village' %}selected{% endif %}>Village Name</option>
                        <option value="category" {% if search_type == 'category' %}selected{% endif %}>Category</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary btn-lg w-100">
                        <i class="fas fa-search me-1"></i>Search
                    </button>
                </div>
            </div>
            
            <!-- Hidden fields to preserve QR context -->
            {% if from_qr %}
            <input type="hidden" name="from_qr" value="true">
            {% endif %}
        </form>
    </div>

    <!-- Search Results -->
    {% if search_performed %}
    <div class="search-results-container">
        <div class="results-header">
            <h4>
                <i class="fas fa-list me-2"></i>Search Results
                {% if search_results %}
                <span class="badge bg-primary ms-2">{{ search_results|length }} found</span>
                {% endif %}
            </h4>
            {% if search_query %}
            <p class="search-summary">
                Searched for "<strong>{{ search_query }}</strong>" in Bundle {{ bundle_number }}
                {% if search_type != 'all' %}({{ search_type.replace('_', ' ').title() }} only){% endif %}
            </p>
            {% endif %}
        </div>

        {% if search_results %}
        <div class="results-grid">
            {% for result in search_results %}
            <div class="result-card">
                <div class="result-header">
                    <h5 class="result-title">{{ result.file.title }}</h5>
                    <div class="result-badges">
                        <span class="badge bg-info">{{ result.village }}</span>
                        {% if result.excel_data.get('RefID') %}
                        <span class="badge bg-secondary">{{ result.excel_data.get('RefID') }}</span>
                        {% endif %}
                    </div>
                </div>

                <div class="result-details">
                    <!-- Location Information -->
                    <div class="detail-section">
                        <h6><i class="fas fa-map-marker-alt me-1"></i>Location</h6>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">Compartment:</span>
                                <span class="detail-value">{{ result.compartment }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Rack:</span>
                                <span class="detail-value">{{ result.rack_number }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Bundle:</span>
                                <span class="detail-value">{{ result.bundle_number }}</span>
                            </div>
                            {% if result.location_details.row != 'N/A' %}
                            <div class="detail-item">
                                <span class="detail-label">Position:</span>
                                <span class="detail-value">Row {{ result.location_details.row }}, Pos {{ result.location_details.position }}</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- File Information -->
                    <div class="detail-section">
                        <h6><i class="fas fa-file-alt me-1"></i>File Details</h6>
                        <div class="detail-grid">
                            {% if result.file.ref_id or result.excel_data.get('RefID') %}
                            <div class="detail-item">
                                <span class="detail-label">RefID:</span>
                                <span class="detail-value">{{ result.file.ref_id or result.excel_data.get('RefID') }}</span>
                            </div>
                            {% endif %}
                            {% if result.file.file_no or result.excel_data.get('FILE_NO') %}
                            <div class="detail-item">
                                <span class="detail-label">File No:</span>
                                <span class="detail-value">{{ result.file.file_no or result.excel_data.get('FILE_NO') }}</span>
                            </div>
                            {% endif %}
                            {% if result.file.survey_no or result.excel_data.get('survey_no') %}
                            <div class="detail-item">
                                <span class="detail-label">Survey No:</span>
                                <span class="detail-value">{{ result.file.survey_no or result.excel_data.get('survey_no') }}</span>
                            </div>
                            {% endif %}
                            {% if result.file.category or result.excel_data.get('Category') %}
                            <div class="detail-item">
                                <span class="detail-label">Category:</span>
                                <span class="detail-value">{{ result.file.category or result.excel_data.get('Category') }}</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="result-actions">
                    <a href="{{ url_for('view_file', file_id=result.file.id) }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>View File
                    </a>
                    <a href="{{ url_for('bundle_detail', bundle_no=result.bundle_number) }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-layer-group me-1"></i>View Bundle
                    </a>
                    {% if result.village != 'Unknown Village' %}
                    <a href="{{ url_for('village_detail', bundle_no=result.bundle_number, village_name=result.village) }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-home me-1"></i>View Village
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="no-results">
            <div class="no-results-icon">
                <i class="fas fa-search"></i>
            </div>
            <h5>No files found</h5>
            <p>No files matching "{{ search_query }}" were found in Bundle {{ bundle_number }}.</p>
            <p>Try searching with different terms or check the search type.</p>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h5><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
        <div class="action-buttons">
            <a href="{{ url_for('bundle_detail', bundle_no=bundle_number) }}" class="btn btn-outline-primary">
                <i class="fas fa-layer-group me-1"></i>View Bundle Details
            </a>
            <a href="{{ url_for('compartment_bundles', compartment_number=bundle_stats.compartment) }}" class="btn btn-outline-info">
                <i class="fas fa-building me-1"></i>View Compartment {{ bundle_stats.compartment }}
            </a>
            <a href="{{ url_for('scan_qrcode') }}" class="btn btn-outline-secondary">
                <i class="fas fa-qrcode me-1"></i>Scan Another QR
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
/* Government Header Styles */
.government-header {
    background: linear-gradient(135deg, #ff9933 0%, #ffffff 50%, #138808 100%);
    border: 2px solid #000080;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.govt-emblem, .state-emblem {
    max-height: 80px;
    width: auto;
}

.govt-title {
    color: #000080;
    font-weight: 700;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.dept-title {
    color: #000080;
    font-weight: 600;
    font-size: 1.4rem;
    margin-bottom: 0.25rem;
}

.dept-subtitle {
    color: #333;
    font-weight: 500;
    font-size: 1rem;
    margin: 0;
}

/* Bundle Header Styles */
.bundle-info-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.bundle-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.bundle-location-info {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.location-badge {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.location-badge.compartment {
    background: rgba(52, 152, 219, 0.3);
}

.location-badge.rack {
    background: rgba(155, 89, 182, 0.3);
}

.location-badge.files {
    background: rgba(46, 204, 113, 0.3);
}

/* Search Form Styles */
.search-form-container {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.search-instructions h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.search-instructions p {
    color: #7f8c8d;
    margin-bottom: 1.5rem;
}

/* Results Styles */
.search-results-container {
    margin-bottom: 2rem;
}

.results-header {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
    border-bottom: 2px solid #e9ecef;
}

.results-header h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.search-summary {
    color: #7f8c8d;
    margin: 0;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
    background: white;
    border-radius: 0 0 12px 12px;
}

.result-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.result-header {
    margin-bottom: 1rem;
}

.result-title {
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.result-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.detail-section {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f1f2f6;
}

.detail-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-section h6 {
    color: #34495e;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
}

.detail-label {
    font-weight: 500;
    color: #7f8c8d;
    font-size: 0.85rem;
}

.detail-value {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.85rem;
}

.result-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f1f2f6;
}

/* No Results Styles */
.no-results {
    text-align: center;
    padding: 3rem;
    background: white;
    border-radius: 0 0 12px 12px;
}

.no-results-icon {
    font-size: 3rem;
    color: #bdc3c7;
    margin-bottom: 1rem;
}

.no-results h5 {
    color: #7f8c8d;
    margin-bottom: 1rem;
}

.no-results p {
    color: #95a5a6;
    margin-bottom: 0.5rem;
}

/* Quick Actions */
.quick-actions {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.quick-actions h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .bundle-title {
        font-size: 1.5rem;
    }

    .bundle-location-info {
        justify-content: center;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .govt-title {
        font-size: 1.4rem;
    }

    .dept-title {
        font-size: 1.2rem;
    }
}
</style>
{% endblock %}
