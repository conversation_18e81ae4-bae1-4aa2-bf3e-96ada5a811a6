{% extends "base.html" %}

{% block title %}Bundle {{ bundle_no }} Details - Taluk Office{% endblock %}

{% block content %}
<style>
    /* Bundle Detail Card Styles */
    .bundle-detail-header {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 2.5rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 25px 25px;
        position: relative;
        overflow: hidden;
    }

    .bundle-detail-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .bundle-detail-header .container {
        position: relative;
        z-index: 1;
    }

    .bundle-breadcrumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .bundle-breadcrumb a {
        color: white;
        text-decoration: none;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }

    .bundle-breadcrumb a:hover {
        opacity: 1;
        color: white;
    }

    .bundle-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .bundle-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.1);
        transition: all 0.3s ease;
        text-align: center;
        position: relative;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #10b981, #059669);
        border-radius: 20px 20px 0 0;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 900;
        color: #10b981;
        margin: 0;
        line-height: 1;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.9rem;
        font-weight: 600;
        margin-top: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .villages-section {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.1);
    }

    .villages-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #10b981;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .villages-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .village-card {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 2px solid #e2e8f0;
        border-radius: 15px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .village-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #10b981, #059669);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .village-card:hover::before {
        transform: scaleX(1);
    }

    .village-card:hover {
        transform: translateY(-5px);
        border-color: #10b981;
        box-shadow: 0 15px 35px rgba(16, 185, 129, 0.15);
    }

    .village-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .village-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: #10b981;
        margin: 0;
    }

    .village-file-count {
        background: #10b981;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .village-categories {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .category-tag {
        background: linear-gradient(135deg, #dcfce7, #bbf7d0);
        color: #166534;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .village-actions {
        display: flex;
        gap: 0.75rem;
        margin-top: 1rem;
    }

    .btn-village-action {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 10px;
        font-size: 0.85rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-village-action:hover {
        background: linear-gradient(135deg, #047857, #059669);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.3;
        color: #10b981;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .bundle-title {
            font-size: 2rem;
        }

        .stats-row {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .villages-grid {
            grid-template-columns: 1fr;
        }

        .village-actions {
            flex-direction: column;
        }
    }
</style>

<!-- Bundle Detail Header -->
<div class="bundle-detail-header">
    <div class="container">
        <!-- Breadcrumb -->
        <div class="bundle-breadcrumb">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('bundle_list') }}">
                            <i class="fas fa-boxes me-1"></i>Bundle Management
                        </a>
                    </li>
                    <li class="breadcrumb-item">Rack {{ rack_no }}</li>
                    <li class="breadcrumb-item active" aria-current="page">Bundle {{ bundle_no }}</li>
                </ol>
            </nav>
        </div>

        <div class="text-center">
            <h1 class="bundle-title">
                <i class="fas fa-folder me-3"></i>Bundle {{ bundle_no }}
            </h1>
            <p class="bundle-subtitle">Rack {{ rack_no }} | {{ bundle_stats.total_files }} Files in {{ bundle_stats.total_villages }} Villages</p>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Row -->
    <div class="stats-row">
        <div class="stat-card">
            <div class="stat-number">{{ rack_no }}</div>
            <div class="stat-label">Rack Number</div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ bundle_no }}</div>
            <div class="stat-label">Bundle Number</div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ bundle_stats.total_villages }}</div>
            <div class="stat-label">Villages</div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ bundle_stats.total_files }}</div>
            <div class="stat-label">Total Files</div>
        </div>
    </div>

    <!-- Villages Section -->
    <div class="villages-section">
        <h3 class="villages-title">
            <i class="fas fa-map-marker-alt"></i>
            Villages in Bundle {{ bundle_no }}
        </h3>

        {% if villages %}
            <div class="villages-grid">
                {% for village_name, village_data in villages.items() %}
                <div class="village-card" onclick="viewVillage('{{ bundle_no }}', '{{ village_name }}')">
                    <div class="village-header">
                        <h4 class="village-name">{{ village_name }}</h4>
                        <div class="village-file-count">{{ village_data.file_count }} files</div>
                    </div>

                    {% if village_data.categories %}
                    <div class="village-categories">
                        {% for category, count in village_data.categories.items() %}
                        <div class="category-tag">
                            {{ category }} ({{ count }})
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="village-actions">
                        <a href="{{ url_for('village_detail', bundle_no=bundle_no, village_name=village_name) }}" class="btn-village-action" onclick="event.stopPropagation()">
                            <i class="fas fa-eye me-1"></i>View Files
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <i class="fas fa-map-marker-alt"></i>
                <h3>No Villages Found</h3>
                <p>This bundle has no files with village information.</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
    function viewVillage(bundleNo, villageName) {
        window.location.href = `/bundles/bundle/${bundleNo}/village/${encodeURIComponent(villageName)}`;
    }
</script>
{% endblock %}
