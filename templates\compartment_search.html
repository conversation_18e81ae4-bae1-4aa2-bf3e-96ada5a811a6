{% extends "base.html" %}

{% block title %}Compartment Search - Taluk Office{% endblock %}

{% block styles %}
<style>
.search-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.compartment-info {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    margin-bottom: 1.5rem;
    text-align: center;
}

.compartment-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-weight: 600;
    display: inline-block;
    margin: 0 0.5rem;
}

.search-form {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.search-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-input, .form-select {
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    transition: var(--transition-normal);
    background: white;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.district-field {
    background: var(--gray-100);
    color: var(--gray-600);
    cursor: not-allowed;
}

.search-actions {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-direction: row;
    margin-top: 2rem;
    align-items: center;
    width: 100%;
}

.search-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 150px;
    justify-content: center;
    white-space: nowrap;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.search-primary {
    background: var(--gradient-primary);
    z-index: 2;
    position: relative;
}

.clear-btn {
    background: var(--gray-100) !important;
    color: var(--gray-700) !important;
    border: 1px solid var(--gray-300) !important;
    z-index: 1;
    position: relative;
}

.clear-btn:hover {
    background: var(--gray-200) !important;
}

.results-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.results-header {
    background: var(--gradient-success);
    color: white;
    padding: 1.5rem 2rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.results-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
}

.results-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

.result-item {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition-fast);
}

.result-item:hover {
    background: var(--gray-50);
}

.result-item:last-child {
    border-bottom: none;
}

.result-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.result-title {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 1.1rem;
    margin: 0;
}

.result-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.action-btn.primary {
    background: var(--primary-color);
    color: white;
}

.action-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.result-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-group {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: var(--radius-lg);
}

.detail-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.detail-value {
    font-weight: 600;
    color: var(--gray-800);
}

.no-results {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--gray-500);
}

.no-results-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.mobile-optimized {
    /* Mobile-specific optimizations */
}

@media (max-width: 768px) {
    .search-grid {
        grid-template-columns: 1fr;
    }

    .search-actions {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .search-btn, .clear-btn {
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
        justify-content: center;
    }

    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .result-details {
        grid-template-columns: 1fr;
    }

    .results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

.search-hint {
    background: var(--info-bg);
    border: 1px solid var(--info-border);
    border-radius: var(--radius-lg);
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: var(--info-color);
}

.search-hint-icon {
    margin-right: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4 mobile-optimized">
    <!-- Header -->
    <div class="search-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="welcome-text">
                    <i class="fas fa-search me-3"></i>Compartment File Search
                </h1>
                <p class="welcome-subtitle">Search files by bundle number, location, or survey number</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{{ url_for('dashboard') }}" class="quick-action-btn">
                    <i class="fas fa-arrow-left"></i>Back to Dashboard
                </a>
            </div>
        </div>

        {% if compartment %}
        <div class="compartment-info">
            <i class="fas fa-archive me-2"></i>
            <span class="compartment-badge">Compartment {{ compartment }}</span>
            {% if range_start and range_end %}
            <span>Bundles {{ range_start }}-{{ range_end }}</span>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <!-- Search Form -->
    <div class="search-form">
        <div class="search-hint">
            <i class="fas fa-info-circle search-hint-icon"></i>
            <strong>Search Tips:</strong> Enter a bundle number to find its exact location, or use location filters to find files in specific areas.
        </div>

        <form method="GET" action="{{ url_for('compartment_search') }}">
            <!-- Preserve compartment info -->
            {% if compartment %}
            <input type="hidden" name="compartment" value="{{ compartment }}">
            <input type="hidden" name="range_start" value="{{ range_start }}">
            <input type="hidden" name="range_end" value="{{ range_end }}">
            {% endif %}

            <div class="search-grid">
                <!-- RefID -->
                <div class="form-group">
                    <label class="form-label" for="ref_id">
                        <i class="fas fa-id-card me-1"></i>Reference ID
                    </label>
                    <input type="text"
                           id="ref_id"
                           name="ref_id"
                           class="form-input"
                           value="{{ ref_id or '' }}"
                           placeholder="Enter reference ID">
                </div>

                <!-- Survey Number -->
                <div class="form-group">
                    <label class="form-label" for="survey_no">
                        <i class="fas fa-map me-1"></i>Survey Number
                    </label>
                    <input type="text"
                           id="survey_no"
                           name="survey_no"
                           class="form-input"
                           value="{{ survey_no or '' }}"
                           placeholder="Enter survey number">
                </div>

                <!-- District (Fixed) -->
                <div class="form-group">
                    <label class="form-label" for="district">
                        <i class="fas fa-map-marker-alt me-1"></i>District
                    </label>
                    <input type="text"
                           id="district"
                           class="form-input district-field"
                           value="Chikkamagaluru"
                           readonly>
                </div>

                <!-- Taluk Name -->
                <div class="form-group">
                    <label class="form-label" for="taluk_name">
                        <i class="fas fa-location-dot me-1"></i>Taluk Name
                    </label>
                    <select id="taluk_name" name="taluk_name" class="form-select">
                        <option value="">Select Taluk</option>
                        {% for taluk in location_data.taluk_names %}
                        <option value="{{ taluk }}" {% if taluk == taluk_name %}selected{% endif %}>{{ taluk }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Hobli Name -->
                <div class="form-group">
                    <label class="form-label" for="hobli_name">
                        <i class="fas fa-map-pin me-1"></i>Hobli Name
                    </label>
                    <select id="hobli_name" name="hobli_name" class="form-select">
                        <option value="">Select Hobli</option>
                        {% for hobli in location_data.hobli_names %}
                        <option value="{{ hobli }}" {% if hobli == hobli_name %}selected{% endif %}>{{ hobli }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Village Name -->
                <div class="form-group">
                    <label class="form-label" for="village_name">
                        <i class="fas fa-home me-1"></i>Village Name
                    </label>
                    <select id="village_name" name="village_name" class="form-select">
                        <option value="">Select Village</option>
                        {% for village in location_data.village_names %}
                        <option value="{{ village }}" {% if village == village_name %}selected{% endif %}>{{ village }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <div class="search-actions">
                <button type="submit" class="search-btn search-primary">
                    <i class="fas fa-search"></i>Search Files
                </button>
                <a href="{{ url_for('compartment_search') }}{% if compartment %}?compartment={{ compartment }}&range_start={{ range_start }}&range_end={{ range_end }}{% endif %}"
                   class="search-btn clear-btn">
                    <i class="fas fa-times"></i>Clear Filters
                </a>
            </div>
        </form>
    </div>

    <!-- Bulk Data Warning -->
    {% if bulk_data_warning %}
    <div class="alert alert-warning bulk-data-warning">
        <div class="d-flex align-items-center">
            <i class="fas fa-shield-alt fa-2x me-3 text-warning"></i>
            <div>
                <h5 class="alert-heading mb-1">Protected Data Detected</h5>
                <p class="mb-2">Some search results contain bulk uploaded government documents that require additional verification.</p>
                <a href="{{ url_for('bulk_data_unlock') }}" class="btn btn-warning btn-sm">
                    <i class="fas fa-unlock me-1"></i>Complete Verification
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Active Session Status -->
    {% if active_session %}
    <div class="alert alert-success session-status">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-check-circle me-2"></i>
                <strong>Bulk Data Access Active</strong> - {{ active_session.time_remaining() }} minutes remaining
            </div>
            <div>
                <button class="btn btn-sm btn-outline-success me-2" onclick="extendSession()">
                    <i class="fas fa-clock me-1"></i>Extend
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="revokeSession()">
                    <i class="fas fa-times me-1"></i>End
                </button>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Search Results -->
    {% if search_performed %}
    <div class="results-section">
        <h2 class="results-header">
            <span><i class="fas fa-file-alt me-2"></i>Search Results</span>
            <span class="results-count">{{ search_results|length }} files found</span>
        </h2>

        {% if search_results %}
        <ul class="results-list">
            {% for result in search_results %}
            <li class="result-item {% if result.is_bulk_data %}bulk-data-item{% endif %}">
                {% if result.is_bulk_data %}
                <div class="bulk-data-indicator">
                    <i class="fas fa-shield-alt"></i>
                    <span>Protected Data</span>
                    {% if result.requires_verification %}
                    <span class="verification-required">Verification Required</span>
                    {% endif %}
                </div>
                {% endif %}

                <div class="result-header">
                    <h3 class="result-title">
                        {{ result.file.title }}
                        {% if result.is_bulk_data and not active_session %}
                        <i class="fas fa-lock text-warning ms-2" title="Requires verification"></i>
                        {% endif %}
                    </h3>
                    <div class="result-actions">
                        {% if result.is_bulk_data and result.requires_verification and not active_session %}
                        <a href="{{ url_for('bulk_data_unlock') }}" class="action-btn warning">
                            <i class="fas fa-unlock"></i>Verify Access
                        </a>
                        {% else %}
                        <a href="{{ url_for('view_file', file_id=result.file.id) }}" class="action-btn primary">
                            <i class="fas fa-eye"></i>View
                        </a>
                        {% endif %}
                        {% if result.file.qr_code %}
                        <a href="{{ url_for('get_qrcode', file_id=result.file.id) }}" class="action-btn secondary" target="_blank">
                            <i class="fas fa-qrcode"></i>QR Code
                        </a>
                        {% endif %}
                    </div>
                </div>

                <div class="result-details">
                    {% if result.parsed_location.ref_id %}
                    <div class="detail-group">
                        <div class="detail-label">Reference ID</div>
                        <div class="detail-value">{{ result.parsed_location.ref_id }}</div>
                    </div>
                    {% endif %}
                    <div class="detail-group">
                        <div class="detail-label">Bundle Number</div>
                        <div class="detail-value">{{ result.bundle_number }}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Row Number</div>
                        <div class="detail-value">{{ result.row_number }}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Position</div>
                        <div class="detail-value">{{ result.position }}</div>
                    </div>
                    {% if result.parsed_location.taluk %}
                    <div class="detail-group">
                        <div class="detail-label">Taluk</div>
                        <div class="detail-value">{{ result.parsed_location.taluk }}</div>
                    </div>
                    {% endif %}
                    {% if result.parsed_location.hobli %}
                    <div class="detail-group">
                        <div class="detail-label">Hobli</div>
                        <div class="detail-value">{{ result.parsed_location.hobli }}</div>
                    </div>
                    {% endif %}
                    {% if result.parsed_location.village %}
                    <div class="detail-group">
                        <div class="detail-label">Village</div>
                        <div class="detail-value">{{ result.parsed_location.village }}</div>
                    </div>
                    {% endif %}
                    {% if result.parsed_location.survey_no %}
                    <div class="detail-group">
                        <div class="detail-label">Survey Number</div>
                        <div class="detail-value">{{ result.parsed_location.survey_no }}</div>
                    </div>
                    {% endif %}
                </div>
            </li>
            {% endfor %}
        </ul>
        {% else %}
        <div class="no-results">
            <div class="no-results-icon">
                <i class="fas fa-search"></i>
            </div>
            <h4>No files found</h4>
            <p>Try adjusting your search criteria with different reference ID, location filters, or survey number.</p>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<style>
    .bulk-data-warning {
        border-left: 4px solid #ffc107;
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        margin-bottom: 1.5rem;
    }

    .session-status {
        border-left: 4px solid #28a745;
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        margin-bottom: 1.5rem;
    }

    .bulk-data-item {
        border-left: 4px solid #ffc107;
        background: linear-gradient(135deg, #fff 0%, #fffbf0 100%);
        position: relative;
    }

    .bulk-data-indicator {
        position: absolute;
        top: 10px;
        right: 15px;
        background: #ffc107;
        color: #212529;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .verification-required {
        background: #dc3545;
        color: white;
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 0.7rem;
        margin-left: 4px;
    }

    .action-btn.warning {
        background: linear-gradient(45deg, #ffc107, #ffca2c);
        color: #212529;
        border: none;
        font-weight: bold;
    }

    .action-btn.warning:hover {
        background: linear-gradient(45deg, #e0a800, #ffb300);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
    }

    .result-title i.fa-lock {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .bulk-data-item:hover {
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const refId = document.getElementById('ref_id').value;
        const talukName = document.getElementById('taluk_name').value;
        const hobliName = document.getElementById('hobli_name').value;
        const villageName = document.getElementById('village_name').value;
        const surveyNo = document.getElementById('survey_no').value;

        // Check if at least one search criteria is provided
        if (!refId && !talukName && !hobliName && !villageName && !surveyNo) {
            e.preventDefault();
            alert('Please enter at least one search criteria.');
            return false;
        }
    });

    // Animate results
    const resultItems = document.querySelectorAll('.result-item');
    resultItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';

        setTimeout(() => {
            item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Session management functions
    {% if active_session %}
    function updateSessionTimer() {
        fetch('/bulk-data-session/status')
            .then(response => response.json())
            .then(data => {
                if (data.has_session) {
                    const statusElement = document.querySelector('.session-status');
                    if (statusElement) {
                        const timeElement = statusElement.querySelector('strong').nextSibling;
                        timeElement.textContent = ` - ${data.time_remaining} minutes remaining`;

                        if (data.time_remaining <= 5) {
                            statusElement.classList.remove('alert-success');
                            statusElement.classList.add('alert-warning');
                        }

                        if (data.time_remaining <= 0) {
                            location.reload();
                        }
                    }
                } else {
                    location.reload();
                }
            })
            .catch(error => console.error('Error updating session timer:', error));
    }

    // Update timer every minute
    setInterval(updateSessionTimer, 60000);
    {% endif %}
});

function extendSession() {
    fetch('/bulk-data-session/extend', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to extend session: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error extending session:', error);
        alert('Error extending session');
    });
}

function revokeSession() {
    if (confirm('Are you sure you want to end your current verification session?')) {
        fetch('/bulk-data-session/revoke', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to revoke session: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error revoking session:', error);
            alert('Error revoking session');
        });
    }
}
</script>
{% endblock %}
