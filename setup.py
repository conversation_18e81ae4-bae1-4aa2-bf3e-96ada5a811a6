#!/usr/bin/env python3
"""
Setup script for Taluk Office Management System
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"⏳ {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def create_directories():
    """Create necessary directories."""
    directories = [
        'static/uploads',
        'static/qrcodes',
        'instance',
        'logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    # Create .gitkeep files to preserve empty directories
    for directory in ['static/uploads', 'static/qrcodes']:
        gitkeep_path = Path(directory) / '.gitkeep'
        gitkeep_path.touch()

def setup_virtual_environment():
    """Set up virtual environment."""
    if not Path('venv').exists():
        if not run_command('python -m venv venv', 'Creating virtual environment'):
            return False
    
    # Activate virtual environment and install requirements
    if os.name == 'nt':  # Windows
        activate_cmd = 'venv\\Scripts\\activate && pip install -r requirements.txt'
    else:  # Unix/Linux/macOS
        activate_cmd = 'source venv/bin/activate && pip install -r requirements.txt'
    
    return run_command(activate_cmd, 'Installing Python dependencies')

def setup_environment_file():
    """Set up environment configuration."""
    if not Path('.env').exists():
        if Path('.env.example').exists():
            shutil.copy('.env.example', '.env')
            print("✓ Created .env file from .env.example")
            print("⚠️  Please edit .env file with your configuration")
        else:
            print("⚠️  No .env.example found, please create .env manually")

def initialize_database():
    """Initialize the database."""
    print("⏳ Initializing database...")
    try:
        # Import here to avoid issues if dependencies aren't installed yet
        from app import app, db
        
        with app.app_context():
            db.create_all()
            print("✓ Database initialized successfully")
        return True
    except Exception as e:
        print(f"✗ Database initialization failed: {e}")
        return False

def main():
    """Main setup function."""
    print("=" * 60)
    print("Taluk Office Management System - Setup")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("✗ Python 3.7 or higher is required")
        sys.exit(1)
    
    print(f"✓ Python {sys.version.split()[0]} detected")
    
    # Create directories
    print("\n📁 Creating directories...")
    create_directories()
    
    # Setup virtual environment
    print("\n🐍 Setting up virtual environment...")
    if not setup_virtual_environment():
        print("✗ Failed to set up virtual environment")
        sys.exit(1)
    
    # Setup environment file
    print("\n⚙️  Setting up environment configuration...")
    setup_environment_file()
    
    # Initialize database
    print("\n🗄️  Initializing database...")
    if not initialize_database():
        print("⚠️  Database initialization failed, you may need to run this manually")
    
    print("\n" + "=" * 60)
    print("✅ Setup completed successfully!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Edit .env file with your configuration")
    print("2. Run: python run.py")
    print("3. Open http://127.0.0.1:5000 in your browser")
    print("\nFor development:")
    print("- Activate virtual environment: venv\\Scripts\\activate (Windows) or source venv/bin/activate (Unix)")
    print("- Run with debug: set DEBUG=True in .env")

if __name__ == '__main__':
    main()
