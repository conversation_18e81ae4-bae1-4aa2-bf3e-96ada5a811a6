{% extends "base.html" %}

{% block title %}Upload Excel File{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Upload Excel File</h4>
                    <small class="text-muted">Automatically create file records from Excel data</small>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages() %}
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-info alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="excel_file" class="form-label">Select Excel File</label>
                            <input type="file" class="form-control" id="excel_file" name="excel_file" 
                                   accept=".xlsx,.xls" required>
                            <div class="form-text">
                                Upload an Excel file (.xlsx or .xls) with the required columns.
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6>Required Excel Columns:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled small">
                                        <li><code>IndexID</code> - Index ID</li>
                                        <li><code>RefID</code> - Reference ID</li>
                                        <li><code>FILE_NO</code> - File Number</li>
                                        <li><code>Category</code> - File Category</li>
                                        <li><code>Year</code> - Year</li>
                                        <li><code>DisposalCat</code> - Disposal Category</li>
                                        <li><code>Createddate</code> - Created Date</li>
                                        <li><code>RowNo</code> - Row Number</li>
                                        <li><code>RackNo</code> - Rack Number</li>
                                        <li><code>RecordRoomSlNo</code> - Record Room Serial Number</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled small">
                                        <li><code>BundleNo</code> - Bundle Number</li>
                                        <li><code>ClosureDate</code> - Closure Date</li>
                                        <li><code>ReceiptAtRRDate</code> - Receipt at RR Date</li>
                                        <li><code>DestructionDate</code> - Destruction Date</li>
                                        <li><code>Subject</code> - Subject/Title</li>
                                        <li><code>dist_name_en</code> - District Name</li>
                                        <li><code>taluk_name_en</code> - Taluk Name</li>
                                        <li><code>hobli_name_en</code> - Hobli Name</li>
                                        <li><code>village_name_en</code> - Village Name</li>
                                        <li><code>survey_no</code> - Survey Number</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6>How it works:</h6>
                            <ul class="mb-0">
                                <li><strong>Title:</strong> Created from FILE_NO, Subject, and Village Name</li>
                                <li><strong>Description:</strong> Created from Category, Year, District, Taluk, Hobli, and Survey Number</li>
                                <li><strong>Location:</strong> Assigned from RackNo, RowNo, and RecordRoomSlNo</li>
                                <li><strong>QR Code:</strong> Automatically generated for each file record</li>
                            </ul>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Upload and Process
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('excel_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const fileName = file.name;
        const fileSize = (file.size / 1024 / 1024).toFixed(2); // Size in MB
        
        // Show file info
        const fileInfo = document.createElement('div');
        fileInfo.className = 'mt-2 text-muted small';
        fileInfo.innerHTML = `Selected: ${fileName} (${fileSize} MB)`;
        
        // Remove any existing file info
        const existingInfo = document.querySelector('.file-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        fileInfo.className += ' file-info';
        e.target.parentNode.appendChild(fileInfo);
    }
});
</script>
{% endblock %}
