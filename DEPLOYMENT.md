# Deployment Guide

## Quick Deployment

### Prerequisites
- Python 3.7 or higher
- pip (Python package manager)
- Git (optional, for cloning)

### Step 1: Setup
```bash
# Run the automated setup
python setup.py
```

### Step 2: Configuration
```bash
# Edit the environment file
# Windows: notepad .env
# Linux/Mac: nano .env

# Set your configuration:
SECRET_KEY=your-unique-secret-key-here
DEBUG=False
HOST=0.0.0.0
PORT=5000
```

### Step 3: Run
```bash
# Start the application
python run.py
```

### Step 4: Access
Open your browser and go to: `http://localhost:5000`

## Production Deployment

### Using Gunicorn (Linux/Mac)
```bash
# Install gunicorn
pip install gunicorn

# Run with gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Using Waitress (Windows)
```bash
# Install waitress
pip install waitress

# Run with waitress
waitress-serve --host=0.0.0.0 --port=5000 app:app
```

### Environment Variables for Production
```env
SECRET_KEY=your-production-secret-key
DEBUG=False
HOST=0.0.0.0
PORT=5000
DATABASE_URL=mysql://user:password@localhost/toffice_db
```

## Docker Deployment (Optional)

### Create Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["python", "run.py"]
```

### Build and Run
```bash
docker build -t toffice .
docker run -p 5000:5000 toffice
```

## Troubleshooting

### Common Issues
1. **Port already in use**: Change PORT in .env file
2. **Database errors**: Check DATABASE_URL configuration
3. **Permission errors**: Ensure write permissions for uploads and database

### Logs
Check the application logs for detailed error information.

## Security Notes
- Change the default SECRET_KEY in production
- Use HTTPS in production
- Regularly backup your database
- Keep dependencies updated
