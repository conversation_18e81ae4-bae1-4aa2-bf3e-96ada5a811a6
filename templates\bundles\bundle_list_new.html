{% extends "base.html" %}

{% block title %}Bundle Management - Taluk Office{% endblock %}

{% block content %}
<style>
        /* Sophisticated Bundle Management Styles */
        .bundle-management-header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 3rem;
            border-radius: 0 0 30px 30px;
            position: relative;
            overflow: hidden;
        }

        .bundle-management-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .bundle-management-header .container {
            position: relative;
            z-index: 1;
        }

        .bundle-management-header h1 {
            font-size: 3rem;
            font-weight: 800;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .bundle-management-header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-top: 0.5rem;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            border-radius: 25px;
            padding: 2.5rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
        }

        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .stat-card .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1.5rem;
        }

        .stat-card .stat-number {
            font-size: 3.5rem;
            font-weight: 900;
            color: #1e3a8a;
            margin: 0;
            line-height: 1;
        }

        .stat-card .stat-icon {
            font-size: 3rem;
            color: #3b82f6;
            opacity: 0.8;
        }

        .stat-card .stat-label {
            color: #6b7280;
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
        }

        .stat-card .stat-description {
            color: #9ca3af;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .racks-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 2.5rem;
            margin-bottom: 3rem;
        }

        .rack-card {
            background: white;
            border-radius: 25px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.4s ease;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .rack-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .rack-header {
            background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%);
            color: white;
            padding: 2.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .rack-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }

        .rack-header:hover::before {
            transform: translateX(100%);
        }

        .rack-title-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .rack-title {
            font-size: 2rem;
            font-weight: 800;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .rack-toggle {
            font-size: 1.8rem;
            transition: transform 0.3s ease;
            opacity: 0.8;
        }

        .rack-card.collapsed .rack-toggle {
            transform: rotate(-90deg);
        }

        .rack-info {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0.5rem;
        }

        .rack-stats {
            display: flex;
            gap: 2.5rem;
            margin-top: 1.5rem;
        }

        .rack-stat {
            text-align: center;
        }

        .rack-stat-number {
            font-size: 1.8rem;
            font-weight: 800;
            margin: 0;
        }

        .rack-stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin: 0;
        }

        .bundles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1.5rem;
            padding: 2.5rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        .rack-card.collapsed .bundles-grid {
            display: none;
        }

        .bundle-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .bundle-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .bundle-card:hover::before {
            transform: scaleX(1);
        }

        .bundle-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: #3b82f6;
        }

        .bundle-number {
            font-size: 2.2rem;
            font-weight: 900;
            color: #1e3a8a;
            margin-bottom: 1rem;
        }

        .bundle-status {
            padding: 0.5rem 1.2rem;
            border-radius: 30px;
            font-size: 0.85rem;
            font-weight: 700;
            margin-bottom: 1rem;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .bundle-status.active {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
        }

        .bundle-status.empty {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            color: #92400e;
        }

        .bundle-file-count {
            color: #6b7280;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .bundle-actions {
            display: flex;
            gap: 0.75rem;
            justify-content: center;
        }

        .btn-view {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 700;
            text-decoration: none;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-view:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .empty-state {
            text-align: center;
            padding: 5rem;
            color: #6b7280;
        }

        .empty-state i {
            font-size: 6rem;
            margin-bottom: 2rem;
            opacity: 0.3;
            color: #3b82f6;
        }

        .empty-state h3 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #374151;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .racks-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .bundles-grid {
                grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
                gap: 1.25rem;
                padding: 2rem;
            }

            .bundle-management-header h1 {
                font-size: 2.2rem;
            }

            .stats-cards {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .rack-stats {
                gap: 1.5rem;
            }
        }
    </style>

<!-- Bundle Management Header -->
<div class="bundle-management-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-archive me-3"></i>Bundle Management System</h1>
            <p class="subtitle">Organize and manage document bundles across 20 racks</p>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-header">
                <div>
                    <div class="stat-number">20</div>
                    <div class="stat-label">Total Racks</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-warehouse"></i>
                </div>
            </div>
            <div class="stat-description">Physical storage racks in the record room</div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div>
                    <div class="stat-number">800</div>
                    <div class="stat-label">Total Bundles</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-folder"></i>
                </div>
            </div>
            <div class="stat-description">Document bundles (40 per rack)</div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div>
                    <div class="stat-number">{{ total_files }}</div>
                    <div class="stat-label">Total Files</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
            </div>
            <div class="stat-description">Documents stored in the system</div>
        </div>
    </div>

    <!-- Racks Container -->
    <div class="racks-container">
        {% for rack_num in range(1, 21) %}
        {% set start_bundle = (rack_num - 1) * 40 + 1 %}
        {% set end_bundle = rack_num * 40 %}
        {% set rack_files = bundles | selectattr('bundle_number', '>=', start_bundle) | selectattr('bundle_number', '<=', end_bundle) | list %}
        {% set total_rack_files = rack_files | sum(attribute='file_count') %}
        
        <div class="rack-card" data-rack="{{ rack_num }}">
            <div class="rack-header" onclick="toggleRack({{ rack_num }})">
                <div>
                    <div class="rack-title-section">
                        <div class="rack-title">
                            <i class="fas fa-chevron-down rack-toggle"></i>
                            Rack {{ rack_num }}
                        </div>
                    </div>
                    <div class="rack-info">Bundles {{ start_bundle }} - {{ end_bundle }}</div>
                    <div class="rack-info">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Row {{ ((rack_num - 1) // 4) + 1 }}, Position {{ ((rack_num - 1) % 4) + 1 }}
                    </div>
                    
                    <div class="rack-stats">
                        <div class="rack-stat">
                            <div class="rack-stat-number">40</div>
                            <div class="rack-stat-label">Bundles</div>
                        </div>
                        <div class="rack-stat">
                            <div class="rack-stat-number">{{ total_rack_files }}</div>
                            <div class="rack-stat-label">Files</div>
                        </div>
                        <div class="rack-stat">
                            <div class="rack-stat-number">{{ "%.0f"|format((total_rack_files / 40) if total_rack_files > 0 else 0) }}</div>
                            <div class="rack-stat-label">Avg/Bundle</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bundles-grid">
                {% for bundle_num in range(start_bundle, end_bundle + 1) %}
                {% set bundle = bundles | selectattr('bundle_number', 'equalto', bundle_num) | first %}
                {% set file_count = bundle.file_count if bundle else 0 %}
                
                <div class="bundle-card" data-bundle="{{ bundle_num }}" onclick="viewBundle({{ bundle_num }})">
                    <div class="bundle-number">{{ bundle_num }}</div>
                    
                    {% if file_count > 0 %}
                    <div class="bundle-status active">Active</div>
                    {% else %}
                    <div class="bundle-status empty">Empty</div>
                    {% endif %}
                    
                    <div class="bundle-file-count">
                        <i class="fas fa-file me-2"></i>{{ file_count }} files
                    </div>
                    
                    <div class="bundle-actions">
                        <a href="{{ url_for('bundle_detail', bundle_no=bundle_num) }}" class="btn-view">
                            <i class="fas fa-eye me-2"></i>View
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<script>
    // Rack toggle functionality
    function toggleRack(rackNum) {
        const rackCard = document.querySelector(`[data-rack="${rackNum}"]`);
        rackCard.classList.toggle('collapsed');
        
        // Save collapsed state to localStorage
        const collapsedRacks = JSON.parse(localStorage.getItem('collapsedRacks') || '[]');
        if (rackCard.classList.contains('collapsed')) {
            if (!collapsedRacks.includes(rackNum)) {
                collapsedRacks.push(rackNum);
            }
        } else {
            const index = collapsedRacks.indexOf(rackNum);
            if (index > -1) {
                collapsedRacks.splice(index, 1);
            }
        }
        localStorage.setItem('collapsedRacks', JSON.stringify(collapsedRacks));
    }
    
    // Bundle view functionality
    function viewBundle(bundleNum) {
        window.location.href = `/bundles/${bundleNum}`;
    }
    
    // Restore collapsed state on page load
    document.addEventListener('DOMContentLoaded', function() {
        const collapsedRacks = JSON.parse(localStorage.getItem('collapsedRacks') || '[]');
        collapsedRacks.forEach(rackNum => {
            const rackCard = document.querySelector(`[data-rack="${rackNum}"]`);
            if (rackCard) {
                rackCard.classList.add('collapsed');
            }
        });
    });
</script>
{% endblock %}
