{% extends "base.html" %}

{% block title %}{{ village_stats.hierarchy_display }} - Files - Taluk Office{% endblock %}

{% block content %}
<style>
    /* Card Style Village Detail */
    .village-detail-header {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 2.5rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 25px 25px;
        position: relative;
        overflow: hidden;
    }

    .village-detail-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .village-detail-header .container {
        position: relative;
        z-index: 1;
    }

    .village-breadcrumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .village-breadcrumb a {
        color: white;
        text-decoration: none;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }

    .village-breadcrumb a:hover {
        opacity: 1;
        color: white;
    }

    .village-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .village-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
    }

    .hierarchy-display {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .hierarchy-arrow {
        color: rgba(255, 255, 255, 0.6);
        font-size: 1.2rem;
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.1);
        transition: all 0.3s ease;
        text-align: center;
        position: relative;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #10b981, #059669);
        border-radius: 20px 20px 0 0;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 900;
        color: #10b981;
        margin: 0;
        line-height: 1;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.9rem;
        font-weight: 600;
        margin-top: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .categories-section {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.1);
    }

    .categories-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #10b981;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .category-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .category-tag {
        background: linear-gradient(135deg, #dcfce7, #bbf7d0);
        color: #166534;
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
        border: 2px solid #bbf7d0;
        transition: all 0.3s ease;
    }

    .category-tag:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
    }

    .files-section {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.1);
    }

    .files-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #10b981;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .files-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 1.5rem;
    }

    .file-card {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 2px solid #e2e8f0;
        border-radius: 15px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .file-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .file-card:hover::before {
        transform: scaleX(1);
    }

    .file-card:hover {
        transform: translateY(-3px);
        border-color: #667eea;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
    }

    .file-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .file-title {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1f2937;
        margin: 0;
        flex: 1;
    }

    .file-id {
        background: #667eea;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .file-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .file-detail {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6b7280;
        font-size: 0.9rem;
    }

    .file-detail i {
        color: #10b981;
        width: 16px;
    }

    .file-actions {
        display: flex;
        gap: 0.75rem;
        margin-top: 1rem;
    }

    .btn-file-action {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 10px;
        font-size: 0.85rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-file-action:hover {
        background: linear-gradient(135deg, #047857, #059669);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6b7280, #4b5563);
    }

    .btn-secondary:hover {
        background: linear-gradient(135deg, #4b5563, #374151);
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.3;
        color: #10b981;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .village-title {
            font-size: 2rem;
        }

        .stats-row {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .files-grid {
            grid-template-columns: 1fr;
        }

        .file-details {
            grid-template-columns: 1fr;
        }

        .file-actions {
            flex-direction: column;
        }

        .hierarchy-display {
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
        }

        .hierarchy-arrow {
            transform: rotate(90deg);
        }
    }
</style>

<!-- Village Detail Header -->
<div class="village-detail-header">
    <div class="container">
        <!-- Breadcrumb -->
        <div class="village-breadcrumb">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('bundle_list') }}">
                            <i class="fas fa-layer-group me-1"></i>Bundle Management
                        </a>
                    </li>
                    <li class="breadcrumb-item">Rack {{ rack_no }}</li>
                    <li class="breadcrumb-item">Bundle {{ bundle_no }}</li>
                    <li class="breadcrumb-item active" aria-current="page">{{ village_name }}</li>
                </ol>
            </nav>
        </div>

        <div class="text-center">
            <h1 class="village-title">
                <i class="fas fa-map-marker-alt me-3"></i>{{ village_name }}
            </h1>
            <p class="village-subtitle">Card Style File Management - {{ village_stats.total_files }} Files</p>
            
            <!-- Hierarchy Display -->
            <div class="hierarchy-display">
                <span><i class="fas fa-warehouse"></i> Rack {{ rack_no }}</span>
                <span class="hierarchy-arrow">→</span>
                <span><i class="fas fa-folder"></i> Bundle {{ bundle_no }}</span>
                <span class="hierarchy-arrow">→</span>
                <span><i class="fas fa-map-marker-alt"></i> {{ village_name }}</span>
                <span class="hierarchy-arrow">→</span>
                <span><i class="fas fa-file-alt"></i> {{ village_stats.total_files }} Files</span>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Row -->
    <div class="stats-row">
        <div class="stat-card">
            <div class="stat-number">{{ rack_no }}</div>
            <div class="stat-label">Rack Number</div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ bundle_no }}</div>
            <div class="stat-label">Bundle Number</div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ village_stats.total_files }}</div>
            <div class="stat-label">Total Files</div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ village_stats.categories|length }}</div>
            <div class="stat-label">Categories</div>
        </div>
    </div>

    <!-- Categories Section -->
    {% if village_stats.categories %}
    <div class="categories-section">
        <h3 class="categories-title">
            <i class="fas fa-tags"></i>
            Document Categories
        </h3>
        <div class="category-tags">
            {% for category, count in village_stats.categories.items() %}
            <div class="category-tag">
                {{ category }} ({{ count }})
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Files Section -->
    <div class="files-section">
        <h3 class="files-title">
            <i class="fas fa-file-alt"></i>
            Files in {{ village_name }}
        </h3>

        {% if files %}
            <div class="files-grid">
                {% for file in files %}
                <div class="file-card" onclick="viewFile({{ file.id }})">
                    <div class="file-header">
                        <h4 class="file-title">{{ file.title or 'Untitled Document' }}</h4>
                        <div class="file-id">ID: {{ file.id }}</div>
                    </div>

                    <div class="file-details">
                        {% if file.file_no %}
                        <div class="file-detail">
                            <i class="fas fa-hashtag"></i>
                            <span>{{ file.file_no }}</span>
                        </div>
                        {% endif %}

                        {% if file.category %}
                        <div class="file-detail">
                            <i class="fas fa-tag"></i>
                            <span>{{ file.category }}</span>
                        </div>
                        {% endif %}

                        {% if file.ref_id %}
                        <div class="file-detail">
                            <i class="fas fa-link"></i>
                            <span>Ref: {{ file.ref_id }}</span>
                        </div>
                        {% endif %}

                        {% if file.created_date %}
                        <div class="file-detail">
                            <i class="fas fa-calendar"></i>
                            <span>{{ file.created_date }}</span>
                        </div>
                        {% endif %}

                        {% if file.survey_no %}
                        <div class="file-detail">
                            <i class="fas fa-map"></i>
                            <span>Survey: {{ file.survey_no }}</span>
                        </div>
                        {% endif %}

                        {% if file.hobli_name %}
                        <div class="file-detail">
                            <i class="fas fa-map-pin"></i>
                            <span>{{ file.hobli_name }}</span>
                        </div>
                        {% endif %}
                    </div>

                    {% if file.description %}
                    <div class="file-detail mb-2">
                        <i class="fas fa-info-circle"></i>
                        <span>{{ file.description[:100] }}{% if file.description|length > 100 %}...{% endif %}</span>
                    </div>
                    {% endif %}

                    <div class="file-actions">
                        <a href="{{ url_for('view_file', file_id=file.id) }}" class="btn-file-action">
                            <i class="fas fa-eye me-1"></i>View
                        </a>
                        <a href="{{ url_for('edit_file', file_id=file.id) }}" class="btn-file-action btn-secondary">
                            <i class="fas fa-edit me-1"></i>Edit
                        </a>
                        {% if file.qr_code %}
                        <a href="{{ url_for('view_qr', file_id=file.id) }}" class="btn-file-action">
                            <i class="fas fa-qrcode me-1"></i>QR Code
                        </a>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <i class="fas fa-folder-open"></i>
                <h3>No Files Found</h3>
                <p>This village has no files in this bundle.</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
    function viewFile(fileId) {
        // Navigate to file detail page
        window.location.href = `/files/${fileId}`;
    }
</script>
{% endblock %}
