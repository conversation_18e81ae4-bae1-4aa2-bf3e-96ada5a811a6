<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Audit Dashboard - T-Office Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .audit-container {
            padding: 2rem 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .stat-card.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .audit-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .section-icon {
            font-size: 1.5rem;
            margin-right: 0.5rem;
            color: #007bff;
        }
        
        .access-log-item {
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .access-log-item.denied {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .access-log-item.bulk-data {
            border-left-color: #ffc107;
            background: #fffbf0;
        }
        
        .log-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .log-user {
            font-weight: bold;
            color: #007bff;
        }
        
        .log-timestamp {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .log-details {
            font-size: 0.9rem;
            color: #495057;
        }
        
        .session-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }
        
        .session-active {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .session-expiring {
            border-color: #ffc107;
            background: #fffbf0;
        }
        
        .session-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .session-user {
            font-weight: bold;
            color: #28a745;
        }
        
        .session-time {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .criteria-badge {
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            margin-right: 0.25rem;
        }
        
        .no-data {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
            transition: all 0.3s;
        }
        
        .refresh-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-shield-alt me-2"></i>T-Office Security Audit
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('admin_bulk_upload') }}">
                    <i class="fas fa-arrow-left me-1"></i>Back to Admin
                </a>
            </div>
        </div>
    </nav>

    <div class="container audit-container">
        <!-- Statistics Overview -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_bulk_files }}</div>
                <div class="stat-label">
                    <i class="fas fa-database me-1"></i>Bulk Data Files
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-number">{{ stats.active_sessions }}</div>
                <div class="stat-label">
                    <i class="fas fa-users me-1"></i>Active Sessions
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-number">{{ stats.recent_access_count }}</div>
                <div class="stat-label">
                    <i class="fas fa-eye me-1"></i>Recent Access
                </div>
            </div>
            <div class="stat-card danger">
                <div class="stat-number">{{ stats.failed_attempts_count }}</div>
                <div class="stat-label">
                    <i class="fas fa-exclamation-triangle me-1"></i>Failed Attempts
                </div>
            </div>
        </div>

        <!-- Active Sessions -->
        <div class="audit-section">
            <div class="section-header">
                <i class="fas fa-clock section-icon"></i>
                <h3>Active Bulk Data Sessions</h3>
            </div>
            
            {% if active_sessions %}
            {% for session in active_sessions %}
            <div class="session-item {% if session.time_remaining() <= 5 %}session-expiring{% else %}session-active{% endif %}">
                <div class="session-header">
                    <div class="session-user">{{ session.user.username }}</div>
                    <div class="session-time">
                        {% if session.time_remaining() > 0 %}
                        <i class="fas fa-clock me-1"></i>{{ session.time_remaining() }} min remaining
                        {% else %}
                        <i class="fas fa-exclamation-triangle text-warning me-1"></i>Expired
                        {% endif %}
                    </div>
                </div>
                <div class="session-details">
                    <small>
                        <strong>Created:</strong> {{ session.created_at.strftime('%Y-%m-%d %H:%M:%S') }} |
                        <strong>Criteria:</strong> {{ session.criteria_count }} |
                        <strong>Access Count:</strong> {{ session.access_count }} |
                        <strong>IP:</strong> {{ session.ip_address or 'Unknown' }}
                    </small>
                </div>
                {% if session.get_unlock_criteria() %}
                <div class="mt-2">
                    {% for key, value in session.get_unlock_criteria().items() %}
                    {% if value %}
                    <span class="criteria-badge">{{ key }}: {{ value }}</span>
                    {% endif %}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
            {% else %}
            <div class="no-data">
                <i class="fas fa-info-circle fa-2x mb-2"></i>
                <p>No active bulk data access sessions</p>
            </div>
            {% endif %}
        </div>

        <!-- Failed Access Attempts -->
        {% if failed_attempts %}
        <div class="audit-section">
            <div class="section-header">
                <i class="fas fa-ban section-icon text-danger"></i>
                <h3>Failed Access Attempts</h3>
            </div>
            
            {% for attempt in failed_attempts %}
            <div class="access-log-item denied">
                <div class="log-header">
                    <div class="log-user">{{ attempt.user.username if attempt.user else 'Anonymous' }}</div>
                    <div class="log-timestamp">{{ attempt.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</div>
                </div>
                <div class="log-details">
                    <strong>Action:</strong> {{ attempt.action }} |
                    <strong>Reason:</strong> {{ attempt.denial_reason or 'Unknown' }} |
                    <strong>IP:</strong> {{ attempt.ip_address or 'Unknown' }}
                    {% if attempt.get_search_criteria() %}
                    <br><strong>Criteria:</strong>
                    {% for key, value in attempt.get_search_criteria().items() %}
                    {% if value %}{{ key }}: {{ value }}{% if not loop.last %}, {% endif %}{% endif %}
                    {% endfor %}
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Recent Access Logs -->
        <div class="audit-section">
            <div class="section-header">
                <i class="fas fa-list section-icon"></i>
                <h3>Recent Bulk Data Access</h3>
            </div>
            
            {% if recent_access %}
            {% for log in recent_access %}
            <div class="access-log-item {% if not log.access_granted %}denied{% else %}bulk-data{% endif %}">
                <div class="log-header">
                    <div class="log-user">{{ log.user.username if log.user else 'Anonymous' }}</div>
                    <div class="log-timestamp">{{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</div>
                </div>
                <div class="log-details">
                    <strong>Action:</strong> {{ log.action }} |
                    <strong>File ID:</strong> {{ log.file_id or 'N/A' }} |
                    <strong>Status:</strong> 
                    {% if log.access_granted %}
                    <span class="text-success">Granted</span>
                    {% else %}
                    <span class="text-danger">Denied</span>
                    {% endif %}
                    <br>
                    <strong>IP:</strong> {{ log.ip_address or 'Unknown' }} |
                    <strong>Session:</strong> {{ log.access_session_id or 'None' }}
                    {% if log.get_search_criteria() %}
                    <br><strong>Search Criteria:</strong>
                    {% for key, value in log.get_search_criteria().items() %}
                    {% if value %}{{ key }}: {{ value }}{% if not loop.last %}, {% endif %}{% endif %}
                    {% endfor %}
                    {% endif %}
                </div>
            </div>
            {% endfor %}
            {% else %}
            <div class="no-data">
                <i class="fas fa-info-circle fa-2x mb-2"></i>
                <p>No recent bulk data access logs</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="location.reload()" title="Refresh Data">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh every 30 seconds
        setInterval(function() {
            location.reload();
        }, 30000);
        
        // Add loading indicator
        document.querySelector('.refresh-btn').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        });
    </script>
</body>
</html>
