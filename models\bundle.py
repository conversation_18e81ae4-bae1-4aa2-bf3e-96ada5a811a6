from extensions import db
from datetime import datetime
import json

class Bundle(db.Model):
    """Bundle model for organizing files by BundleNo"""
    __tablename__ = 'bundles'

    id = db.Column(db.Integer, primary_key=True)
    bundle_number = db.Column(db.Integer, nullable=False, unique=True)  # 1-800
    bundle_name = db.Column(db.String(255))  # Optional custom name
    description = db.Column(db.Text)

    # Capacity and organization
    max_capacity = db.Column(db.Integer, default=100)  # Maximum files per bundle
    current_count = db.Column(db.Integer, default=0)   # Current file count

    # Physical location
    compartment_id = db.Column(db.Integer, db.<PERSON>ey('compartment_qrs.id'))
    rack_number = db.Column(db.String(10))
    row_number = db.Column(db.String(10))
    position = db.Column(db.String(10))

    # Bundle QR code
    qr_code_path = db.Column(db.String(255))
    qr_data = db.Column(db.Text)  # JSON data for QR code

    # Status and metadata
    is_active = db.Column(db.<PERSON>, default=True)
    is_full = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    compartment = db.relationship('CompartmentQR', backref='bundles')

    def __repr__(self):
        return f'<Bundle {self.bundle_number}: {self.current_count}/{self.max_capacity} files>'

    def get_files(self):
        """Get all files in this bundle"""
        from models.file import File
        files = []

        # Query files that have this bundle number in their excel_row_data
        all_files = File.query.filter(File.excel_row_data.isnot(None)).all()

        for file in all_files:
            try:
                excel_data = json.loads(file.excel_row_data)
                bundle_no = excel_data.get('BundleNo', '')

                # Handle different formats of bundle number
                if bundle_no:
                    # Convert to int for comparison
                    try:
                        file_bundle_no = int(float(str(bundle_no)))
                        if file_bundle_no == self.bundle_number:
                            files.append(file)
                    except (ValueError, TypeError):
                        # Handle non-numeric bundle numbers
                        if str(bundle_no).strip() == str(self.bundle_number):
                            files.append(file)
            except (json.JSONDecodeError, AttributeError):
                continue

        return files

    def update_file_count(self):
        """Update the current file count for this bundle"""
        files = self.get_files()
        self.current_count = len(files)
        self.is_full = self.current_count >= self.max_capacity
        db.session.commit()
        return self.current_count

    def get_capacity_percentage(self):
        """Get capacity usage as percentage"""
        if self.max_capacity == 0:
            return 0
        return min(100, (self.current_count / self.max_capacity) * 100)

    def get_status(self):
        """Get bundle status"""
        if self.current_count == 0:
            return 'empty'
        elif self.is_full:
            return 'full'
        elif self.current_count >= self.max_capacity * 0.8:
            return 'nearly_full'
        else:
            return 'active'

    @property
    def compartment_number(self):
        """Get compartment number for this bundle"""
        if self.compartment:
            return self.compartment.compartment_number
        # Fallback: calculate based on bundle number
        return 1 if self.bundle_number <= 400 else 2

    @property
    def file_count(self):
        """Get current file count for this bundle"""
        return self.current_count

    @property
    def calculated_rack_number(self):
        """Calculate rack number based on bundle number"""
        return ((self.bundle_number - 1) // 40) + 1

    def get_compartment_info(self):
        """Get compartment information for this bundle"""
        if self.compartment:
            return {
                'compartment_number': self.compartment.compartment_number,
                'bundle_range': f"{self.compartment.bundle_range_start}-{self.compartment.bundle_range_end}"
            }
        return None

    def generate_qr_code(self):
        """Generate STATIC QR code for this bundle with compartment-specific URL

        IMPORTANT: This method generates IMMUTABLE QR codes that never change.
        The QR code content is based ONLY on bundle number and compartment assignment,
        NOT on dynamic data like file count or capacity utilization.
        """
        # Determine compartment based on bundle number (STATIC assignment)
        compartment_number = 1 if self.bundle_number <= 400 else 2

        # STATIC QR code data - contains ONLY immutable information
        qr_data = {
            'type': 'T-Office-Bundle',
            'bundle_number': self.bundle_number,
            'compartment': compartment_number,
            'url': f"/compartment-qr/{compartment_number}/bundle/{self.bundle_number}",
            'description': f"Bundle {self.bundle_number} in Compartment {compartment_number}"
        }

        self.qr_data = json.dumps(qr_data)
        return qr_data

    def get_qr_code_url(self):
        """Get the URL that this bundle's QR code should redirect to"""
        compartment_number = 1 if self.bundle_number <= 400 else 2
        return f"/compartment-qr/{compartment_number}/bundle/{self.bundle_number}"

    def get_qr_code_filename(self):
        """Get the filename for this bundle's QR code"""
        return f"bundle_{self.bundle_number}_qr.png"


class BundleAssignment(db.Model):
    """Track file assignments to bundles for audit purposes"""
    __tablename__ = 'bundle_assignments'

    id = db.Column(db.Integer, primary_key=True)
    file_id = db.Column(db.Integer, db.ForeignKey('files.id'), nullable=False)
    bundle_id = db.Column(db.Integer, db.ForeignKey('bundles.id'), nullable=False)

    # Assignment details
    assigned_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    assignment_type = db.Column(db.String(50), default='automatic')  # automatic, manual, reassignment
    assignment_reason = db.Column(db.Text)

    # Timestamps
    assigned_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Previous assignment (for reassignments)
    previous_bundle_id = db.Column(db.Integer, db.ForeignKey('bundles.id'))

    # Relationships
    file = db.relationship('File', backref='bundle_assignments')
    bundle = db.relationship('Bundle', foreign_keys=[bundle_id], backref='file_assignments')
    previous_bundle = db.relationship('Bundle', foreign_keys=[previous_bundle_id])
    assigned_by_user = db.relationship('User', backref='bundle_assignments')

    def __repr__(self):
        return f'<BundleAssignment File {self.file_id} -> Bundle {self.bundle_id}>'


class BundleStatistics(db.Model):
    """Store bundle statistics for reporting and analytics"""
    __tablename__ = 'bundle_statistics'

    id = db.Column(db.Integer, primary_key=True)
    bundle_id = db.Column(db.Integer, db.ForeignKey('bundles.id'), nullable=False)

    # Statistics
    total_files = db.Column(db.Integer, default=0)
    files_with_physical_documents = db.Column(db.Integer, default=0)
    files_excel_only = db.Column(db.Integer, default=0)

    # Data quality metrics
    avg_data_quality_score = db.Column(db.Float, default=0.0)
    files_with_warnings = db.Column(db.Integer, default=0)

    # Access statistics
    total_access_count = db.Column(db.Integer, default=0)
    last_accessed = db.Column(db.DateTime)
    most_accessed_file_id = db.Column(db.Integer, db.ForeignKey('files.id'))

    # Time tracking
    calculated_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    bundle = db.relationship('Bundle', backref='statistics')
    most_accessed_file = db.relationship('File')

    def __repr__(self):
        return f'<BundleStatistics Bundle {self.bundle_id}: {self.total_files} files>'

    @classmethod
    def calculate_for_bundle(cls, bundle_id):
        """Calculate and update statistics for a bundle"""
        from models.file import File
        from models.access_log import AccessLog

        bundle = Bundle.query.get(bundle_id)
        if not bundle:
            return None

        files = bundle.get_files()

        # Calculate statistics
        total_files = len(files)
        files_with_physical = sum(1 for f in files if f.filename)
        files_excel_only = total_files - files_with_physical

        # Data quality
        quality_scores = [f.data_quality_score for f in files if f.data_quality_score]
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        files_with_warnings = sum(1 for f in files if f.import_warnings)

        # Access statistics
        file_ids = [f.id for f in files]
        access_logs = AccessLog.query.filter(AccessLog.file_id.in_(file_ids)).all()
        total_access = len(access_logs)

        last_access = None
        most_accessed_file_id = None
        if access_logs:
            last_access = max(log.timestamp for log in access_logs)

            # Find most accessed file
            file_access_counts = {}
            for log in access_logs:
                file_access_counts[log.file_id] = file_access_counts.get(log.file_id, 0) + 1

            if file_access_counts:
                most_accessed_file_id = max(file_access_counts, key=file_access_counts.get)

        # Update or create statistics record
        stats = cls.query.filter_by(bundle_id=bundle_id).first()
        if not stats:
            stats = cls(bundle_id=bundle_id)
            db.session.add(stats)

        stats.total_files = total_files
        stats.files_with_physical_documents = files_with_physical
        stats.files_excel_only = files_excel_only
        stats.avg_data_quality_score = avg_quality
        stats.files_with_warnings = files_with_warnings
        stats.total_access_count = total_access
        stats.last_accessed = last_access
        stats.most_accessed_file_id = most_accessed_file_id
        stats.calculated_at = datetime.utcnow()

        db.session.commit()
        return stats
