{% extends "base.html" %}

{% block title %}Compartment QR Codes - Taluk Office{% endblock %}

{% block styles %}
<style>
.compartment-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.compartment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.compartment-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition-normal);
}

.compartment-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.compartment-card-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.compartment-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.compartment-status {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
}

.compartment-card-body {
    padding: 2rem;
}

.qr-preview {
    text-align: center;
    margin-bottom: 1.5rem;
}

.qr-image {
    max-width: 200px;
    height: auto;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 1rem;
    background: white;
}

.compartment-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.info-item {
    text-align: center;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.info-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.info-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
}

.compartment-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-normal);
    cursor: pointer;
}

.action-btn.primary {
    background: var(--gradient-primary);
    color: white;
}

.action-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--gray-500);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.generation-info {
    background: var(--info-bg);
    border: 1px solid var(--info-border);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.generation-info h4 {
    color: var(--info-color);
    margin-bottom: 0.5rem;
}

.generation-info p {
    margin: 0;
    color: var(--gray-700);
}

.cli-command {
    background: var(--gray-900);
    color: var(--gray-100);
    padding: 1rem;
    border-radius: var(--radius-md);
    font-family: 'Courier New', monospace;
    margin-top: 1rem;
    overflow-x: auto;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Header -->
    <div class="compartment-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="welcome-text">Compartment QR Codes</h1>
                <p class="welcome-subtitle">Manage static QR codes for file compartments</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{{ url_for('dashboard') }}" class="quick-action-btn">
                    <i class="fas fa-arrow-left"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    {% if not compartment_qrs %}
    <!-- Generation Info -->
    <div class="generation-info">
        <h4><i class="fas fa-info-circle me-2"></i>Generate Compartment QR Codes</h4>
        <p>No compartment QR codes have been generated yet. Use the Flask CLI command to generate static QR codes for compartments:</p>
        <div class="cli-command">
            flask generate-compartment-qrcodes
        </div>
    </div>

    <!-- Empty State -->
    <div class="empty-state">
        <div class="empty-icon">
            <i class="fas fa-qrcode"></i>
        </div>
        <h4>No Compartment QR Codes</h4>
        <p>Generate compartment QR codes using the CLI command above to get started.</p>
    </div>
    {% else %}
    <!-- Compartment QR Codes Grid -->
    <div class="compartment-grid">
        {% for qr in compartment_qrs %}
        <div class="compartment-card">
            <div class="compartment-card-header">
                <h3 class="compartment-title">
                    <i class="fas fa-archive me-2"></i>Compartment {{ qr.compartment_number }}
                </h3>
                <span class="compartment-status">
                    {% if qr.is_active %}Active{% else %}Inactive{% endif %}
                </span>
            </div>
            
            <div class="compartment-card-body">
                <!-- QR Code Preview -->
                <div class="qr-preview">
                    <img src="{{ url_for('get_compartment_qr_image', compartment_number=qr.compartment_number) }}" 
                         alt="Compartment {{ qr.compartment_number }} QR Code" 
                         class="qr-image">
                </div>
                
                <!-- Compartment Info -->
                <div class="compartment-info">
                    <div class="info-item">
                        <div class="info-label">Bundle Range</div>
                        <div class="info-value">{{ qr.bundle_range_start }}-{{ qr.bundle_range_end }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Total Bundles</div>
                        <div class="info-value">{{ qr.get_bundle_list()|length }}</div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="compartment-actions">
                    <a href="{{ url_for('view_compartment_qr', compartment_number=qr.compartment_number) }}" 
                       class="action-btn primary">
                        <i class="fas fa-eye"></i>View Details
                    </a>
                    <a href="{{ url_for('download_compartment_qr', compartment_number=qr.compartment_number) }}" 
                       class="action-btn secondary">
                        <i class="fas fa-download"></i>Download
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Additional Info -->
    <div class="generation-info">
        <h4><i class="fas fa-terminal me-2"></i>CLI Commands</h4>
        <p>Use these Flask CLI commands to manage compartment QR codes:</p>
        <div class="cli-command">
            # List all compartment QR codes<br>
            flask list-compartment-qrcodes<br><br>
            # Regenerate a specific compartment QR code<br>
            flask regenerate-compartment-qr &lt;compartment_number&gt;
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate compartment cards
    const cards = document.querySelectorAll('.compartment-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});
</script>
{% endblock %}
