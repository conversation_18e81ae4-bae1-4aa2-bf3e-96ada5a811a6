<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Data Access Verification - T-Office</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .security-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .security-card {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .security-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .security-icon {
            font-size: 3rem;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        
        .criteria-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .criteria-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .criteria-title {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .criteria-title i {
            margin-right: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-control {
            border-radius: 6px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        
        .unlock-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            transition: all 0.3s;
            width: 100%;
        }
        
        .unlock-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40,167,69,0.4);
        }
        
        .session-status {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .session-expired {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .requirements-list {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .requirements-list h5 {
            color: #856404;
            margin-bottom: 1rem;
        }
        
        .requirements-list ul {
            margin-bottom: 0;
            color: #856404;
        }
        
        .security-warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
            color: #721c24;
        }
        
        @media (max-width: 768px) {
            .criteria-grid {
                grid-template-columns: 1fr;
            }
            
            .security-container {
                margin: 1rem;
                padding: 1rem;
            }
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-shield-alt me-2"></i>T-Office Security
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="security-container">
            <div class="security-card">
                <div class="security-header">
                    <div class="security-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <h2>Bulk Data Access Verification</h2>
                    <p class="text-muted">Additional verification required to access bulk uploaded government documents</p>
                </div>

                {% if active_session %}
                <div class="session-status">
                    <h5><i class="fas fa-check-circle text-success me-2"></i>Active Verification Session</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Time Remaining:</strong> <span id="time-remaining">{{ active_session.time_remaining() }}</span> minutes
                        </div>
                        <div class="col-md-6">
                            <strong>Files Accessed:</strong> {{ active_session.access_count }}
                        </div>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-outline-success me-2" onclick="extendSession()">
                            <i class="fas fa-clock me-1"></i>Extend Session
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="revokeSession()">
                            <i class="fas fa-times me-1"></i>End Session
                        </button>
                    </div>
                </div>
                {% endif %}

                <div class="security-warning">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Security Notice</h5>
                    <p class="mb-0">
                        You are attempting to access bulk uploaded government documents. This requires additional verification 
                        to ensure data security and compliance with access control policies.
                    </p>
                </div>

                <div class="requirements-list">
                    <h5><i class="fas fa-list-check me-2"></i>Verification Requirements</h5>
                    <ul>
                        <li>Provide at least <strong>2 search criteria</strong> from the options below</li>
                        <li>One of the following combinations must be complete:
                            <ul>
                                <li><strong>RefID</strong> (exact match), OR</li>
                                <li><strong>Complete location data</strong> (Hobli + Village + Survey Number), OR</li>
                                <li><strong>File number with date range</strong></li>
                            </ul>
                        </li>
                        <li>Session will expire after <strong>30 minutes</strong> of inactivity</li>
                        <li>All access attempts are logged for security audit</li>
                    </ul>
                </div>

                <form method="POST" id="unlock-form">
                    <div class="criteria-grid">
                        <div class="criteria-section">
                            <div class="criteria-title">
                                <i class="fas fa-hashtag"></i>Reference Information
                            </div>
                            <div class="form-group">
                                <label for="ref_id" class="form-label">Reference ID</label>
                                <input type="text" class="form-control" id="ref_id" name="ref_id" 
                                       placeholder="Enter exact RefID">
                                <small class="form-text text-muted">Must be exact match</small>
                            </div>
                            <div class="form-group">
                                <label for="file_no" class="form-label">File Number</label>
                                <input type="text" class="form-control" id="file_no" name="file_no" 
                                       placeholder="Enter file number">
                            </div>
                            <div class="form-group">
                                <label for="date_range" class="form-label">Date Range</label>
                                <input type="text" class="form-control" id="date_range" name="date_range" 
                                       placeholder="e.g., 2023-2024">
                                <small class="form-text text-muted">Required with file number</small>
                            </div>
                        </div>

                        <div class="criteria-section">
                            <div class="criteria-title">
                                <i class="fas fa-map-marker-alt"></i>Location Information
                            </div>
                            <div class="form-group">
                                <label for="hobli_name" class="form-label">Hobli Name</label>
                                <input type="text" class="form-control" id="hobli_name" name="hobli_name" 
                                       placeholder="Enter hobli name">
                            </div>
                            <div class="form-group">
                                <label for="village_name" class="form-label">Village Name</label>
                                <input type="text" class="form-control" id="village_name" name="village_name" 
                                       placeholder="Enter village name">
                            </div>
                            <div class="form-group">
                                <label for="survey_no" class="form-label">Survey Number</label>
                                <input type="text" class="form-control" id="survey_no" name="survey_no" 
                                       placeholder="Enter survey number">
                                <small class="form-text text-muted">Required with hobli and village</small>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="unlock-btn">
                            <i class="fas fa-unlock me-2"></i>Verify and Unlock Access
                        </button>
                    </div>
                </form>

                <div class="mt-4 text-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        This verification creates a temporary 30-minute session for accessing bulk uploaded data.
                        All activities are logged for security compliance.
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Update session timer if active
        {% if active_session %}
        function updateTimer() {
            fetch('/bulk-data-session/status')
                .then(response => response.json())
                .then(data => {
                    if (data.has_session) {
                        document.getElementById('time-remaining').textContent = data.time_remaining;
                        if (data.time_remaining <= 0) {
                            location.reload();
                        }
                    } else {
                        location.reload();
                    }
                })
                .catch(error => console.error('Error updating timer:', error));
        }
        
        // Update timer every minute
        setInterval(updateTimer, 60000);
        {% endif %}
        
        function extendSession() {
            fetch('/bulk-data-session/extend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Session extended successfully');
                    updateTimer();
                } else {
                    alert('Failed to extend session: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error extending session:', error);
                alert('Error extending session');
            });
        }
        
        function revokeSession() {
            if (confirm('Are you sure you want to end your current session?')) {
                fetch('/bulk-data-session/revoke', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Failed to revoke session: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error revoking session:', error);
                    alert('Error revoking session');
                });
            }
        }
        
        // Form validation
        document.getElementById('unlock-form').addEventListener('submit', function(e) {
            const formData = new FormData(this);
            const criteria = {};
            let filledCount = 0;
            
            for (let [key, value] of formData.entries()) {
                if (value.trim()) {
                    criteria[key] = value.trim();
                    filledCount++;
                }
            }
            
            if (filledCount < 2) {
                e.preventDefault();
                alert('Please provide at least 2 search criteria for verification.');
                return false;
            }
            
            // Check for valid combinations
            const hasRefId = criteria.ref_id;
            const hasCompleteLocation = criteria.hobli_name && criteria.village_name && criteria.survey_no;
            const hasFileWithDate = criteria.file_no && criteria.date_range;
            
            if (!hasRefId && !hasCompleteLocation && !hasFileWithDate) {
                e.preventDefault();
                alert('Please provide one of the required combinations:\n' +
                      '• RefID (exact match), OR\n' +
                      '• Complete location (Hobli + Village + Survey Number), OR\n' +
                      '• File number with date range');
                return false;
            }
            
            return true;
        });
    </script>
</body>
</html>
