{% extends "base.html" %}

{% block title %}Taluk Office - Digital File Management System{% endblock %}

{% block content %}
<style>
    /* Sophisticated Home Page Styles */
    .hero-section {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #8b5cf6 100%);
        color: white;
        padding: 5rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
        min-height: 80vh;
        display: flex;
        align-items: center;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="15" height="15" patternUnits="userSpaceOnUse"><path d="M 15 0 L 0 0 0 15" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .hero-section::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 30% 70%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 70% 30%, rgba(59, 130, 246, 0.3) 0%, transparent 50%);
    }

    .hero-content {
        position: relative;
        z-index: 1;
    }

    .govt-emblem {
        width: 150px;
        height: 150px;
        margin: 0 auto 2rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .govt-emblem i {
        font-size: 4rem;
        color: white;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .hero-section h1 {
        font-size: 4rem;
        font-weight: 900;
        margin-bottom: 1rem;
        text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        letter-spacing: -1px;
    }

    .hero-section .lead {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        opacity: 0.95;
        font-weight: 300;
    }

    .hero-section .subtitle {
        font-size: 1.1rem;
        margin-bottom: 3rem;
        opacity: 0.8;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .btn-hero {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 1.2rem 3rem;
        border-radius: 50px;
        font-size: 1.2rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.4s ease;
        backdrop-filter: blur(10px);
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .btn-hero:hover {
        background: white;
        color: #1e3a8a;
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        border-color: white;
    }

    .dashboard-section {
        padding: 4rem 0;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .dashboard-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .dashboard-card {
        background: white;
        border-radius: 25px;
        padding: 2.5rem;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.4s ease;
        border: 1px solid rgba(59, 130, 246, 0.1);
        position: relative;
        overflow: hidden;
    }

    .dashboard-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    }

    .dashboard-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    }

    .dashboard-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        color: white;
        font-size: 2.5rem;
        box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
    }

    .dashboard-card h4 {
        color: #1e3a8a;
        margin-bottom: 1rem;
        font-weight: 700;
        font-size: 1.3rem;
    }

    .dashboard-card p {
        color: #6b7280;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .btn-dashboard {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 15px;
        font-size: 1rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-dashboard:hover {
        background: linear-gradient(135deg, #2563eb, #1d4ed8);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
    }

    .stats-section {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        color: white;
        padding: 4rem 0;
        position: relative;
        overflow: hidden;
    }

    .stats-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    }

    .stats-container {
        position: relative;
        z-index: 1;
    }

    .stat-item {
        text-align: center;
        padding: 2rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.15);
    }

    .stat-number {
        font-size: 3.5rem;
        font-weight: 900;
        margin-bottom: 0.5rem;
        display: block;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .stat-label {
        font-size: 1.2rem;
        opacity: 0.9;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .text-gradient {
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-section h1 {
            font-size: 2.5rem;
        }

        .dashboard-cards {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .govt-emblem {
            width: 120px;
            height: 120px;
        }

        .govt-emblem i {
            font-size: 3rem;
        }
    }
</style>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <div class="govt-emblem">
                <i class="fas fa-university"></i>
            </div>
            <h1>Government of Karnataka</h1>
            <p class="lead">Taluk Office Digital File Management System</p>
            <p class="subtitle">Secure, efficient, and transparent document management for government operations with advanced bundle organization and digital tracking</p>
            {% if not current_user.is_authenticated %}
            <a href="{{ url_for('login') }}" class="btn-hero">
                <i class="fas fa-sign-in-alt me-2"></i>Access System
            </a>
            {% endif %}
        </div>
    </div>
</section>

<!-- Dashboard Section for Authenticated Users -->
{% if current_user.is_authenticated %}
<section class="dashboard-section">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-4 fw-bold text-gradient mb-3">Digital Operations Dashboard</h2>
            <p class="lead text-muted">Manage documents, bundles, and administrative tasks efficiently</p>
        </div>
        
        <div class="dashboard-cards">
            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h4>Document Registry</h4>
                <p>Register and manage official government documents with digital tracking and QR code generation.</p>
                <a href="{{ url_for('add_file') }}" class="btn-dashboard">
                    <i class="fas fa-plus me-2"></i>Register Document
                </a>
            </div>
            
            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-archive"></i>
                </div>
                <h4>Bundle Management</h4>
                <p>Organize files into bundles across 20 racks with sophisticated tracking and management tools.</p>
                <a href="{{ url_for('bundle_list') }}" class="btn-dashboard">
                    <i class="fas fa-folder me-2"></i>Manage Bundles
                </a>
            </div>
            
            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h4>Document Search</h4>
                <p>Locate documents quickly using QR codes, reference numbers, or advanced search criteria.</p>
                <a href="{{ url_for('scan_qrcode') }}" class="btn-dashboard">
                    <i class="fas fa-search me-2"></i>Search Documents
                </a>
            </div>
            
            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-upload"></i>
                </div>
                <h4>Excel Upload</h4>
                <p>Bulk upload documents from Excel files with automatic bundle organization and validation.</p>
                <a href="{{ url_for('upload_excel') }}" class="btn-dashboard">
                    <i class="fas fa-file-excel me-2"></i>Upload Excel
                </a>
            </div>
            
            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h4>Analytics & Reports</h4>
                <p>Generate comprehensive reports and view statistical analysis of document processing.</p>
                <a href="{{ url_for('analytics') }}" class="btn-dashboard">
                    <i class="fas fa-chart-bar me-2"></i>View Analytics
                </a>
            </div>
            
            <div class="dashboard-card">
                <div class="dashboard-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <h4>System Administration</h4>
                <p>Manage users, system settings, and administrative functions for the file management system.</p>
                {% if current_user.role == 'Administrator' %}
                <a href="{{ url_for('admin_dashboard') }}" class="btn-dashboard">
                    <i class="fas fa-tools me-2"></i>Admin Panel
                </a>
                {% else %}
                <span class="btn-dashboard" style="opacity: 0.5; cursor: not-allowed;">
                    <i class="fas fa-lock me-2"></i>Admin Only
                </span>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="stats-section">
    <div class="container stats-container">
        <div class="text-center mb-5">
            <h2 class="display-4 fw-bold mb-3">System Statistics</h2>
            <p class="lead opacity-75">Real-time overview of document management operations</p>
        </div>
        
        <div class="row g-4">
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">{{ file_stats.total_files if file_stats else 0 }}</span>
                    <span class="stat-label">Total Files</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">{{ file_stats.files_today if file_stats else 0 }}</span>
                    <span class="stat-label">Files Today</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">{{ user_stats.total_users if user_stats else 0 }}</span>
                    <span class="stat-label">Active Users</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">800</span>
                    <span class="stat-label">Total Bundles</span>
                </div>
            </div>
        </div>
    </div>
</section>
{% endif %}

{% endblock %}
