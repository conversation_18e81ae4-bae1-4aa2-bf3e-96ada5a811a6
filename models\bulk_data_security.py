from extensions import db
from datetime import datetime, timedelta
import uuid
import json
from cryptography.fernet import Fernet
import os
import base64

class BulkDataAccessSession(db.Model):
    """Manages temporary access sessions for bulk uploaded data"""
    __tablename__ = 'bulk_data_access_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    session_token = db.Column(db.String(255), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Session details
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=False)
    last_activity = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Bo<PERSON>, default=True)
    
    # Access criteria that granted this session
    unlock_criteria = db.Column(db.Text)  # JSON of criteria used to unlock
    criteria_count = db.Column(db.Integer, default=0)  # Number of criteria provided
    
    # Session metadata
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    access_count = db.Column(db.Integer, default=0)  # Number of files accessed in this session
    
    # Relationships
    user = db.relationship('User', backref='bulk_access_sessions')
    
    def __init__(self, user_id, unlock_criteria=None, duration_minutes=30):
        self.user_id = user_id
        self.session_token = str(uuid.uuid4())
        self.created_at = datetime.utcnow()
        self.expires_at = self.created_at + timedelta(minutes=duration_minutes)
        self.last_activity = self.created_at
        
        if unlock_criteria:
            self.set_unlock_criteria(unlock_criteria)
    
    def set_unlock_criteria(self, criteria_dict):
        """Store unlock criteria as JSON"""
        if criteria_dict:
            self.unlock_criteria = json.dumps(criteria_dict)
            # Count non-empty criteria
            self.criteria_count = len([v for v in criteria_dict.values() if v and str(v).strip()])
    
    def get_unlock_criteria(self):
        """Retrieve unlock criteria as dictionary"""
        if self.unlock_criteria:
            try:
                return json.loads(self.unlock_criteria)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def is_valid(self):
        """Check if session is still valid"""
        now = datetime.utcnow()
        return (self.is_active and 
                self.expires_at > now and 
                (now - self.last_activity).total_seconds() < 1800)  # 30 minutes inactivity
    
    def extend_session(self, minutes=30):
        """Extend session expiry time"""
        if self.is_valid():
            self.expires_at = datetime.utcnow() + timedelta(minutes=minutes)
            self.last_activity = datetime.utcnow()
            return True
        return False
    
    def record_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.utcnow()
        self.access_count += 1
    
    def revoke(self):
        """Revoke the session"""
        self.is_active = False
    
    def time_remaining(self):
        """Get remaining time in minutes"""
        if not self.is_valid():
            return 0
        remaining = (self.expires_at - datetime.utcnow()).total_seconds() / 60
        return max(0, int(remaining))
    
    def __repr__(self):
        return f'<BulkDataAccessSession {self.session_token[:8]}... for User {self.user_id}>'


class DataEncryption:
    """Utility class for encrypting sensitive data in bulk uploads"""
    
    @staticmethod
    def get_encryption_key():
        """Get or generate encryption key"""
        key_file = 'data_encryption.key'
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # Generate new key
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    @staticmethod
    def encrypt_sensitive_data(data_dict):
        """Encrypt sensitive fields in excel_row_data"""
        if not data_dict:
            return data_dict
        
        try:
            key = DataEncryption.get_encryption_key()
            fernet = Fernet(key)
            
            # Fields to encrypt
            sensitive_fields = ['RefID', 'FILE_NO', 'survey_no', 'IndexID']
            
            encrypted_data = data_dict.copy()
            for field in sensitive_fields:
                if field in encrypted_data and encrypted_data[field]:
                    # Convert to string and encrypt
                    field_value = str(encrypted_data[field])
                    encrypted_value = fernet.encrypt(field_value.encode())
                    encrypted_data[f'{field}_encrypted'] = base64.b64encode(encrypted_value).decode()
                    # Keep original for Administrator access, mark as encrypted
                    encrypted_data[f'{field}_is_encrypted'] = True
            
            return encrypted_data
        except Exception as e:
            # If encryption fails, return original data with warning
            data_dict['encryption_error'] = str(e)
            return data_dict
    
    @staticmethod
    def decrypt_sensitive_data(encrypted_data_dict, user_role='Clerk'):
        """Decrypt sensitive fields based on user role"""
        if not encrypted_data_dict:
            return encrypted_data_dict
        
        try:
            key = DataEncryption.get_encryption_key()
            fernet = Fernet(key)
            
            decrypted_data = encrypted_data_dict.copy()
            
            # Only Administrators can see full decrypted data
            if user_role == 'Administrator':
                sensitive_fields = ['RefID', 'FILE_NO', 'survey_no', 'IndexID']
                for field in sensitive_fields:
                    encrypted_field = f'{field}_encrypted'
                    if encrypted_field in decrypted_data:
                        try:
                            encrypted_value = base64.b64decode(decrypted_data[encrypted_field])
                            decrypted_value = fernet.decrypt(encrypted_value).decode()
                            decrypted_data[field] = decrypted_value
                        except Exception:
                            # If decryption fails, keep original
                            pass
            else:
                # For non-Administrators, apply data masking
                decrypted_data = DataEncryption.mask_sensitive_data(decrypted_data)
            
            return decrypted_data
        except Exception:
            # If decryption fails, apply masking for safety
            return DataEncryption.mask_sensitive_data(encrypted_data_dict)
    
    @staticmethod
    def mask_sensitive_data(data_dict):
        """Apply data masking for non-Administrator users"""
        if not data_dict:
            return data_dict
        
        masked_data = data_dict.copy()
        
        # Mask RefID (show only first 2 and last 2 characters)
        if 'RefID' in masked_data and masked_data['RefID']:
            ref_id = str(masked_data['RefID'])
            if len(ref_id) > 4:
                masked_data['RefID'] = f"{ref_id[:2]}***{ref_id[-2:]}"
            else:
                masked_data['RefID'] = "***"
        
        # Mask survey_no (show only first digit)
        if 'survey_no' in masked_data and masked_data['survey_no']:
            survey = str(masked_data['survey_no'])
            if len(survey) > 1:
                masked_data['survey_no'] = f"{survey[0]}***"
            else:
                masked_data['survey_no'] = "***"
        
        # Mask FILE_NO (show only prefix)
        if 'FILE_NO' in masked_data and masked_data['FILE_NO']:
            file_no = str(masked_data['FILE_NO'])
            if '/' in file_no:
                parts = file_no.split('/')
                masked_data['FILE_NO'] = f"{parts[0]}/***"
            else:
                masked_data['FILE_NO'] = "***"
        
        return masked_data


class BulkDataAccessLog(db.Model):
    """Enhanced logging specifically for bulk data access attempts"""
    __tablename__ = 'bulk_data_access_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    session_id = db.Column(db.Integer, db.ForeignKey('bulk_data_access_sessions.id'))
    
    # Access attempt details
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    action = db.Column(db.String(50), nullable=False)  # unlock_attempt, access_granted, access_denied, session_expired
    search_criteria = db.Column(db.Text)  # JSON of search criteria
    criteria_count = db.Column(db.Integer, default=0)
    
    # Result details
    files_accessed = db.Column(db.Integer, default=0)
    access_granted = db.Column(db.Boolean, default=False)
    denial_reason = db.Column(db.String(255))
    
    # Security metadata
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    
    # Relationships
    user = db.relationship('User', backref='bulk_access_logs')
    session = db.relationship('BulkDataAccessSession', backref='access_logs')
    
    def set_search_criteria(self, criteria_dict):
        """Store search criteria as JSON"""
        if criteria_dict:
            self.search_criteria = json.dumps(criteria_dict)
            self.criteria_count = len([v for v in criteria_dict.values() if v and str(v).strip()])
    
    def get_search_criteria(self):
        """Retrieve search criteria as dictionary"""
        if self.search_criteria:
            try:
                return json.loads(self.search_criteria)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def __repr__(self):
        return f'<BulkDataAccessLog {self.action} by User {self.user_id} at {self.timestamp}>'
