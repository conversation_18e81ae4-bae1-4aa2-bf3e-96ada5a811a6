{% extends "base.html" %}

{% block title %}Edit File - {{ file.title }}{% endblock %}

{% block styles %}
<style>
.edit-file-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.page-header {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    text-align: center;
}

.page-title {
    color: #2c3e50;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 10px 0;
}

.page-subtitle {
    color: #7f8c8d;
    font-size: 16px;
    margin: 0;
}

.edit-form-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.form-section {
    margin-bottom: 30px;
}

.section-title {
    color: #2c3e50;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    outline: none;
}

.form-control.textarea {
    min-height: 100px;
    resize: vertical;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-row.three-col {
    grid-template-columns: 1fr 1fr 1fr;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-2px);
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.file-info-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.file-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    text-align: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.info-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
    font-weight: 500;
}

.info-value {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.required {
    color: #e74c3c;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-row.three-col {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="edit-file-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-edit me-3"></i>Edit File Details
        </h1>
        <p class="page-subtitle">Update file information and metadata</p>
    </div>

    <!-- File Info Card -->
    <div class="file-info-card">
        <h4 style="margin-bottom: 15px; color: #2c3e50;">
            <i class="fas fa-info-circle me-2"></i>Current File Information
        </h4>
        <div class="file-info-grid">
            <div class="info-item">
                <div class="info-label">File ID</div>
                <div class="info-value">{{ file.id }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Created Date</div>
                <div class="info-value">{{ file.created_at.strftime('%Y-%m-%d') if file.created_at else 'N/A' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Last Updated</div>
                <div class="info-value">{{ file.updated_at.strftime('%Y-%m-%d') if file.updated_at else 'N/A' }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Current Bundle</div>
                <div class="info-value">{{ file.bundle_no or 'Not Assigned' }}</div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="edit-form-card">
        <form method="POST" id="editFileForm">
            <!-- Basic Information Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-file-alt"></i>Basic Information
                </h3>
                
                <div class="form-group">
                    <label for="title" class="form-label">Title <span class="required">*</span></label>
                    <input type="text" class="form-control" id="title" name="title" 
                           value="{{ file.title }}" required>
                </div>
                
                <div class="form-group">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control textarea" id="description" name="description" 
                              placeholder="Enter file description...">{{ file.description or '' }}</textarea>
                </div>
                
                <div class="form-group">
                    <label for="subject" class="form-label">Subject</label>
                    <textarea class="form-control" id="subject" name="subject" 
                              placeholder="Enter file subject...">{{ file.subject or '' }}</textarea>
                </div>
            </div>

            <!-- Classification Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-tags"></i>Classification
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-control" id="category" name="category">
                            <option value="">Select Category</option>
                            <option value="Revenue" {{ 'selected' if file.category == 'Revenue' else '' }}>Revenue</option>
                            <option value="Land Records" {{ 'selected' if file.category == 'Land Records' else '' }}>Land Records</option>
                            <option value="Certificates" {{ 'selected' if file.category == 'Certificates' else '' }}>Certificates</option>
                            <option value="Legal" {{ 'selected' if file.category == 'Legal' else '' }}>Legal</option>
                            <option value="Administrative" {{ 'selected' if file.category == 'Administrative' else '' }}>Administrative</option>
                            <option value="Other" {{ 'selected' if file.category == 'Other' else '' }}>Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="ref_id" class="form-label">Reference ID</label>
                        <input type="text" class="form-control" id="ref_id" name="ref_id" 
                               value="{{ file.ref_id or '' }}" placeholder="Enter reference ID">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="file_no" class="form-label">File Number</label>
                    <input type="text" class="form-control" id="file_no" name="file_no" 
                           value="{{ file.file_no or '' }}" placeholder="Enter file number">
                </div>
            </div>

            <!-- Location Information Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-map-marker-alt"></i>Location Information
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="village_name" class="form-label">Village Name</label>
                        <input type="text" class="form-control" id="village_name" name="village_name" 
                               value="{{ file.village_name or '' }}" placeholder="Enter village name">
                    </div>
                    
                    <div class="form-group">
                        <label for="hobli_name" class="form-label">Hobli Name</label>
                        <input type="text" class="form-control" id="hobli_name" name="hobli_name" 
                               value="{{ file.hobli_name or '' }}" placeholder="Enter hobli name">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="survey_no" class="form-label">Survey Number</label>
                    <input type="text" class="form-control" id="survey_no" name="survey_no" 
                           value="{{ file.survey_no or '' }}" placeholder="Enter survey number">
                </div>
            </div>

            <!-- Physical Location Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-archive"></i>Physical Storage
                </h3>
                
                <div class="form-row three-col">
                    <div class="form-group">
                        <label for="bundle_no" class="form-label">Bundle Number</label>
                        <input type="number" class="form-control" id="bundle_no" name="bundle_no" 
                               value="{{ file.bundle_no or '' }}" min="1" max="800" 
                               placeholder="1-800">
                    </div>
                    
                    <div class="form-group">
                        <label for="rack_no" class="form-label">Rack Number</label>
                        <input type="number" class="form-control" id="rack_no" name="rack_no" 
                               value="{{ file.rack_no or '' }}" min="1" max="20" 
                               placeholder="1-20">
                    </div>
                    
                    <div class="form-group">
                        <label for="row_no" class="form-label">Row Number</label>
                        <input type="number" class="form-control" id="row_no" name="row_no" 
                               value="{{ file.row_no or '' }}" min="1" 
                               placeholder="Row number">
                    </div>
                </div>
            </div>

            <!-- Hidden field to track return destination -->
            <input type="hidden" name="return_to_village" value="{{ request.args.get('return_to_village', '') }}">

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>Save Changes
                </button>
                
                <a href="{{ url_for('view_file', file_id=file.id) }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i>Cancel
                </a>
                
                {% if current_user.role == 'Administrator' %}
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                    <i class="fas fa-trash"></i>Delete File
                </button>
                {% endif %}
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
{% if current_user.role == 'Administrator' %}
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this file?</p>
                <p><strong>Title:</strong> {{ file.title }}</p>
                <p><strong>File ID:</strong> {{ file.id }}</p>
                <p class="text-danger"><strong>This action cannot be undone!</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ url_for('delete_file', file_id=file.id) }}" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete File</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function confirmDelete() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Form validation
document.getElementById('editFileForm').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    
    if (!title) {
        e.preventDefault();
        alert('Title is required!');
        document.getElementById('title').focus();
        return false;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    submitBtn.disabled = true;
    
    // Re-enable button after 5 seconds in case of issues
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
});

// Auto-calculate rack number based on bundle number
document.getElementById('bundle_no').addEventListener('input', function() {
    const bundleNo = parseInt(this.value);
    if (bundleNo && bundleNo >= 1 && bundleNo <= 800) {
        const rackNo = Math.ceil(bundleNo / 40);
        document.getElementById('rack_no').value = rackNo;
    }
});
</script>
{% endblock %}
