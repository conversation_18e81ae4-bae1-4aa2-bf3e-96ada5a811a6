#!/usr/bin/env python3
"""
Taluk Office Management System - Main Application Runner
"""

import os
import sys
from app import app, db
from extensions import socketio
from flask_migrate import upgrade

def create_app():
    """Create and configure the Flask application."""
    
    # Ensure required directories exist
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['QR_CODE_FOLDER'], exist_ok=True)
    
    # Create database tables if they don't exist
    with app.app_context():
        try:
            db.create_all()
            print("✓ Database tables created successfully")
        except Exception as e:
            print(f"✗ Error creating database tables: {e}")
            return None
    
    return app

def main():
    """Main application entry point."""
    print("=" * 50)
    print("Taluk Office Management System")
    print("=" * 50)
    
    # Create the application
    application = create_app()
    if not application:
        print("✗ Failed to create application")
        sys.exit(1)
    
    # Get configuration
    host = os.environ.get('HOST', '127.0.0.1')
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'False').lower() == 'true'
    
    print(f"✓ Starting server on http://{host}:{port}")
    print(f"✓ Debug mode: {debug}")
    print("✓ Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Run the application with Socket.IO support
        socketio.run(
            application,
            host=host,
            port=port,
            debug=debug,
            use_reloader=debug,
            log_output=True
        )
    except KeyboardInterrupt:
        print("\n✓ Server stopped by user")
    except Exception as e:
        print(f"✗ Server error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
