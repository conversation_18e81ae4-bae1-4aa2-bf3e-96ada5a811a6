{% extends "base.html" %}

{% block title %}Admin Bulk Upload - Taluk Office{% endblock %}

{% block styles %}
<style>
.admin-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
    border-left: 4px solid var(--danger-color);
}

.upload-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.requirements-section {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.column-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-top: 1rem;
}

.column-item {
    background: var(--white);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-300);
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.upload-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.file-input-group {
    position: relative;
}

.file-input {
    width: 100%;
    padding: 2rem;
    border: 3px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--gray-50);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.file-input:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.file-input input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    top: 0;
    left: 0;
}

.file-input.dragover {
    border-color: var(--primary-color);
    background: var(--primary-light);
    transform: scale(1.02);
}

.file-input.file-selected {
    border-color: var(--success-color);
    background: var(--success-light);
}

.file-input.file-error {
    border-color: var(--danger-color);
    background: var(--danger-light);
}

.file-input.processing {
    border-color: var(--primary-color);
    background: var(--primary-light);
    opacity: 0.8;
    pointer-events: none;
}

.upload-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.upload-btn:disabled {
    background: var(--gray-400);
    cursor: not-allowed;
    transform: none;
}

.summary-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: var(--gray-50);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    text-align: center;
    border: 1px solid var(--gray-200);
}

.summary-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.summary-label {
    color: var(--gray-600);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.success-number { color: var(--success-color); }
.warning-number { color: var(--warning-color); }
.error-number { color: var(--danger-color); }
.info-number { color: var(--primary-color); }

.errors-section {
    background: var(--danger-light);
    border: 1px solid var(--danger-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-top: 2rem;
}

.error-list {
    list-style: none;
    padding: 0;
    margin: 1rem 0 0 0;
}

.error-item {
    background: var(--white);
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    border-radius: var(--radius-md);
    border-left: 4px solid var(--danger-color);
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-md);
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.admin-warning {
    background: var(--warning-light);
    border: 1px solid var(--warning-color);
    border-radius: var(--radius-lg);
    padding: 1rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-warning i {
    color: var(--warning-color);
    font-size: 1.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Header -->
    <div class="admin-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="welcome-text">
                    <i class="fas fa-shield-alt me-3"></i>Admin Bulk Upload
                </h1>
                <p class="welcome-subtitle">Upload Excel files with comprehensive file records</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{{ url_for('dashboard') }}" class="quick-action-btn">
                    <i class="fas fa-arrow-left"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Admin Warning -->
    <div class="admin-warning">
        <i class="fas fa-exclamation-triangle"></i>
        <div>
            <strong>Administrator Access Required</strong><br>
            This interface is restricted to administrators only. Use with caution when processing large datasets.
        </div>
    </div>

    <!-- Requirements Section -->
    <div class="requirements-section">
        <h3><i class="fas fa-list-check me-2"></i>Required Excel Column Structure</h3>
        <p>Your Excel file must contain exactly these 15 columns in any order:</p>

        <div class="column-list">
            <div class="column-item">SL No</div>
            <div class="column-item">INDEX ID</div>
            <div class="column-item">RefID</div>
            <div class="column-item">FILE_NO</div>
            <div class="column-item">Category</div>
            <div class="column-item">DisposalCat</div>
            <div class="column-item">Createddate</div>
            <div class="column-item">RowNo</div>
            <div class="column-item">RackNo</div>
            <div class="column-item">RecordRoomSlNo</div>
            <div class="column-item">BundleNo</div>
            <div class="column-item">Subject</div>
            <div class="column-item">hobli_name</div>
            <div class="column-item">village_name</div>
            <div class="column-item">survey_no</div>
        </div>

        <div style="margin-top: 1.5rem; padding: 1rem; background: var(--success-light); border-radius: var(--radius-md); border: 1px solid var(--success-color);">
            <h5><i class="fas fa-shield-alt me-2"></i>🎯 100% Data Preservation Mode</h5>
            <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                <li><strong>✅ ALL ROWS IMPORTED:</strong> No data is ever skipped or discarded</li>
                <li><strong>✅ DUPLICATE PRESERVATION:</strong> Multiple RefIDs are allowed and preserved</li>
                <li><strong>✅ UNLIMITED TEXT:</strong> No character limits - complete data stored</li>
                <li><strong>✅ ERROR RECOVERY:</strong> Failed rows get fallback records with complete data</li>
                <li><strong>✅ QUALITY TRACKING:</strong> Issues flagged but don't prevent import</li>
                <li><strong>✅ SEARCH INTEGRATION:</strong> All data immediately searchable via RefID/location</li>
                <li><strong>✅ BATCH TRACKING:</strong> Complete audit trail with unique batch IDs</li>
                <li><strong>✅ JSON PRESERVATION:</strong> Original Excel data stored for recovery</li>
            </ul>

            <div style="margin-top: 1rem; text-align: center; display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <a href="{{ url_for('download_bulk_upload_template') }}" class="upload-btn" style="display: inline-flex; text-decoration: none;">
                    <i class="fas fa-download"></i>
                    Download Excel Template
                </a>
                <a href="{{ url_for('migrate_database_for_bulk_upload') }}" class="upload-btn" style="display: inline-flex; text-decoration: none; background: var(--warning-color);">
                    <i class="fas fa-database"></i>
                    Migrate Database
                </a>
                <a href="{{ url_for('fix_database_schema') }}" class="upload-btn" style="display: inline-flex; text-decoration: none; background: var(--danger-color);">
                    <i class="fas fa-wrench"></i>
                    Fix Database Schema
                </a>
            </div>
            <p style="text-align: center; margin-top: 0.5rem; font-size: 0.875rem; color: var(--gray-600);">
                Run database migration once before first bulk upload. Use "Fix Database Schema" if you encounter column errors.
            </p>
        </div>
    </div>

    <!-- Upload Section -->
    <div class="upload-section">
        <h3><i class="fas fa-upload me-2"></i>Upload Excel File</h3>

        <form method="POST" enctype="multipart/form-data" class="upload-form" id="uploadForm">
            <div class="file-input-group">
                <div class="file-input" id="fileInputArea">
                    <i class="fas fa-file-excel" style="font-size: 2rem; color: var(--success-color); margin-bottom: 1rem;"></i>
                    <p><strong>Click to select Excel file</strong> or drag and drop</p>
                    <p style="color: var(--gray-600); font-size: 0.875rem;">Supports .xlsx and .xls files</p>
                    <input type="file" name="excel_file" accept=".xlsx,.xls" required id="fileInput">
                </div>
                <div id="fileName" style="margin-top: 1rem; font-weight: 600; color: var(--primary-color);"></div>
            </div>

            <button type="submit" class="upload-btn" id="uploadBtn" disabled>
                <i class="fas fa-cloud-upload-alt"></i>
                Process Excel File
            </button>
        </form>
    </div>

    <!-- Summary Section (shown after upload) -->
    {% if summary %}
    <div class="summary-section">
        <h3><i class="fas fa-chart-bar me-2"></i>Processing Summary</h3>

        <div class="summary-grid">
            <div class="summary-card">
                <div class="summary-number info-number">{{ summary.total_rows }}</div>
                <div class="summary-label">Total Rows</div>
            </div>
            <div class="summary-card">
                <div class="summary-number success-number">{{ summary.created_count }}</div>
                <div class="summary-label">Records Created</div>
            </div>
            <div class="summary-card">
                <div class="summary-number warning-number">{{ summary.warnings_count or 0 }}</div>
                <div class="summary-label">Quality Warnings</div>
            </div>
            <div class="summary-card">
                <div class="summary-number {% if summary.success_rate == 100 %}success-number{% elif summary.success_rate >= 95 %}warning-number{% else %}error-number{% endif %}">{{ summary.success_rate or 0 }}%</div>
                <div class="summary-label">Success Rate</div>
            </div>
        </div>

        <!-- Data Preservation Status -->
        <div style="margin: 2rem 0; padding: 1.5rem; background: {% if summary.data_preservation_status == 'COMPLETE' %}var(--success-light){% else %}var(--warning-light){% endif %}; border-radius: var(--radius-lg); border: 1px solid {% if summary.data_preservation_status == 'COMPLETE' %}var(--success-color){% else %}var(--warning-color){% endif %};">
            <h4 style="margin: 0 0 1rem 0; color: {% if summary.data_preservation_status == 'COMPLETE' %}var(--success-color){% else %}var(--warning-color){% endif %};">
                <i class="fas {% if summary.data_preservation_status == 'COMPLETE' %}fa-check-circle{% else %}fa-exclamation-triangle{% endif %} me-2"></i>
                Data Preservation Status: {{ summary.data_preservation_status }}
            </h4>
            <p style="margin: 0.5rem 0;">
                <strong>Import Method:</strong> {{ summary.import_method or '100% Data Preservation Mode' }}<br>
                <strong>Batch ID:</strong> {{ summary.batch_id or 'N/A' }}<br>
                <strong>Records Preserved:</strong> {{ summary.created_count }} of {{ summary.total_rows }} ({{ summary.success_rate or 0 }}%)
            </p>
            {% if summary.data_preservation_status == 'COMPLETE' %}
            <p style="color: var(--success-color); font-weight: 600; margin: 1rem 0 0 0;">
                ✅ All data successfully preserved with zero data loss!
            </p>
            {% else %}
            <p style="color: var(--warning-color); font-weight: 600; margin: 1rem 0 0 0;">
                ⚠️ Some rows had issues but data was preserved with fallback records.
            </p>
            {% endif %}
        </div>

        <!-- Compartment Assignment Summary -->
        {% if summary.compartment_assignment %}
        <div style="margin: 2rem 0; padding: 1.5rem; background: var(--primary-light); border-radius: var(--radius-lg); border: 1px solid var(--primary-color);">
            <h4 style="margin: 0 0 1rem 0; color: var(--primary-color);">
                <i class="fas fa-archive me-2"></i>Compartment Assignment Summary
            </h4>

            <div class="summary-grid" style="margin-bottom: 1.5rem;">
                <div class="summary-card">
                    <div class="summary-number success-number">{{ summary.compartment_assignment.assignment_rate }}%</div>
                    <div class="summary-label">Assignment Rate</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number info-number">{{ summary.compartment_assignment.compartment_1_files }}</div>
                    <div class="summary-label">Compartment 1</div>
                    <div style="font-size: 0.75rem; color: var(--gray-600);">Bundles 1-400</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number info-number">{{ summary.compartment_assignment.compartment_2_files }}</div>
                    <div class="summary-label">Compartment 2</div>
                    <div style="font-size: 0.75rem; color: var(--gray-600);">Bundles 401-800</div>
                </div>
                {% if summary.compartment_assignment.unassigned_files > 0 %}
                <div class="summary-card">
                    <div class="summary-number warning-number">{{ summary.compartment_assignment.unassigned_files }}</div>
                    <div class="summary-label">Unassigned</div>
                    <div style="font-size: 0.75rem; color: var(--gray-600);">Missing Bundle Numbers</div>
                </div>
                {% endif %}
            </div>

            <!-- Quick Access Buttons -->
            <div style="display: flex; gap: 1rem; flex-wrap: wrap; justify-content: center;">
                {% if summary.compartment_assignment.compartment_1_files > 0 %}
                <a href="{{ summary.compartment_assignment.compartment_1_url }}"
                   style="display: flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; background: var(--success-color); color: white; text-decoration: none; border-radius: var(--radius-md); font-weight: 600; transition: var(--transition-normal);"
                   onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'"
                   onmouseout="this.style.transform=''; this.style.boxShadow=''">
                    <i class="fas fa-archive"></i>
                    <div>
                        <div>View Compartment 1</div>
                        <div style="font-size: 0.75rem; opacity: 0.9;">{{ summary.compartment_assignment.compartment_1_files }} files</div>
                    </div>
                </a>
                {% endif %}

                {% if summary.compartment_assignment.compartment_2_files > 0 %}
                <a href="{{ summary.compartment_assignment.compartment_2_url }}"
                   style="display: flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; background: var(--primary-color); color: white; text-decoration: none; border-radius: var(--radius-md); font-weight: 600; transition: var(--transition-normal);"
                   onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'"
                   onmouseout="this.style.transform=''; this.style.boxShadow=''">
                    <i class="fas fa-archive"></i>
                    <div>
                        <div>View Compartment 2</div>
                        <div style="font-size: 0.75rem; opacity: 0.9;">{{ summary.compartment_assignment.compartment_2_files }} files</div>
                    </div>
                </a>
                {% endif %}
            </div>

            <p style="margin: 1rem 0 0 0; font-size: 0.875rem; color: var(--gray-600); text-align: center;">
                Click on compartment buttons to view uploaded files organized by bundle numbers
            </p>
        </div>
        {% endif %}

        {% if summary.created_count > 0 %}
        <div class="progress-bar">
            <div class="progress-fill" style="width: {{ (summary.created_count / summary.total_rows * 100) | round(1) }}%;"></div>
        </div>
        <p style="text-align: center; color: var(--gray-600);">
            Success Rate: {{ (summary.created_count / summary.total_rows * 100) | round(1) }}%
        </p>
        {% endif %}

        {% if summary.warnings %}
        <div class="errors-section" style="background: var(--warning-light); border-color: var(--warning-color);">
            <h4><i class="fas fa-exclamation-triangle me-2"></i>Data Quality Warnings</h4>
            <p>First {{ summary.warnings|length }} warnings (data was preserved despite these issues):</p>
            <ul class="error-list">
                {% for warning in summary.warnings %}
                <li class="error-item" style="border-left-color: var(--warning-color);">{{ warning }}</li>
                {% endfor %}
            </ul>
            <p style="margin-top: 1rem; font-weight: 600; color: var(--warning-color);">
                ℹ️ All warnings indicate data quality issues that were flagged but did not prevent data import.
            </p>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('fileInput');
    const fileInputArea = document.getElementById('fileInputArea');
    const fileName = document.getElementById('fileName');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadForm = document.getElementById('uploadForm');

    // CRITICAL FIX: Click handler to trigger file dialog
    fileInputArea.addEventListener('click', function(e) {
        // Prevent event bubbling and trigger file input
        e.preventDefault();
        fileInput.click();
    });

    // File input change handler
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Reset classes
            fileInputArea.className = 'file-input';

            // Validate file type
            const validTypes = ['.xlsx', '.xls'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            if (!validTypes.includes(fileExtension)) {
                fileName.textContent = `❌ Invalid file type. Please select .xlsx or .xls files only.`;
                fileName.style.color = 'var(--danger-color)';
                fileInputArea.classList.add('file-error');
                fileInput.value = ''; // Clear the invalid file
                return;
            }

            // Valid file selected
            fileName.textContent = `✅ Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
            fileName.style.color = 'var(--success-color)';
            fileInputArea.classList.add('file-selected');

            // Enable upload button
            uploadBtn.disabled = false;
        } else {
            // No file selected
            fileName.textContent = '';
            fileInputArea.className = 'file-input';
            uploadBtn.disabled = true;
        }
    });

    // Drag and drop handlers
    fileInputArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        fileInputArea.classList.add('dragover');
    });

    fileInputArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        // Only remove dragover if we're actually leaving the drop zone
        if (!fileInputArea.contains(e.relatedTarget)) {
            fileInputArea.classList.remove('dragover');
        }
    });

    fileInputArea.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        fileInputArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];

            // Reset classes
            fileInputArea.className = 'file-input';

            // Validate file type
            const validTypes = ['.xlsx', '.xls'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            if (!validTypes.includes(fileExtension)) {
                fileName.textContent = `❌ Invalid file type. Please select .xlsx or .xls files only.`;
                fileName.style.color = 'var(--danger-color)';
                fileInputArea.classList.add('file-error');
                return;
            }

            // Set the file to the input
            fileInput.files = files;
            fileName.textContent = `✅ Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
            fileName.style.color = 'var(--success-color)';
            fileInputArea.classList.add('file-selected');

            // Enable upload button
            uploadBtn.disabled = false;
        }
    });

    // Form submission handler with validation
    uploadForm.addEventListener('submit', function(e) {
        // Validate file is selected
        if (!fileInput.files || fileInput.files.length === 0) {
            e.preventDefault();
            fileName.textContent = '❌ Please select an Excel file before uploading.';
            fileName.style.color = 'var(--danger-color)';
            fileInputArea.classList.add('file-error');
            return false;
        }

        // Validate file type one more time
        const file = fileInput.files[0];
        const validTypes = ['.xlsx', '.xls'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!validTypes.includes(fileExtension)) {
            e.preventDefault();
            fileName.textContent = '❌ Invalid file type. Please select .xlsx or .xls files only.';
            fileName.style.color = 'var(--danger-color)';
            fileInputArea.classList.add('file-error');
            return false;
        }

        // Show processing state
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing Excel File...';

        // Show progress message
        fileName.textContent = `🔄 Processing ${file.name}... Please wait.`;
        fileName.style.color = 'var(--primary-color)';

        // Add processing class to upload area
        fileInputArea.classList.remove('file-selected', 'file-error');
        fileInputArea.classList.add('processing');
    });

    // Initialize upload button state
    uploadBtn.disabled = true;
});
</script>
{% endblock %}
