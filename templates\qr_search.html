{% extends "base.html" %}

{% block title %}QR Search - Taluk Office{% endblock %}

{% block content %}
<div class="qr-search-page">
    <!-- Header Section -->
    <div class="page-header">
        <div class="container-fluid px-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="page-title">
                        <i class="fas fa-search-plus me-3"></i>QR Search Interface
                    </h1>
                    <p class="page-subtitle">Scan the QR code below to access the search interface</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="page-actions">
                        <a href="{{ url_for('global_search') }}" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>Direct Search
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- QR Code Section -->
    <div class="container-fluid px-4 py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="qr-search-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-qrcode me-2"></i>Search Interface QR Code
                        </h3>
                        <p class="card-subtitle">
                            This QR code will open the search interface when scanned
                        </p>
                    </div>
                    
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="qr-display">
                                    <div id="qr-code-container">
                                        <div class="qr-loading">
                                            <i class="fas fa-spinner fa-spin"></i>
                                            <p>Generating QR Code...</p>
                                        </div>
                                    </div>
                                    <div class="qr-actions">
                                        <button onclick="downloadQR()" class="btn btn-primary">
                                            <i class="fas fa-download me-2"></i>Download QR
                                        </button>
                                        <button onclick="printQR()" class="btn btn-secondary">
                                            <i class="fas fa-print me-2"></i>Print QR
                                        </button>
                                        <button onclick="refreshQR()" class="btn btn-outline-primary">
                                            <i class="fas fa-sync me-2"></i>Refresh
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="qr-info">
                                    <h4><i class="fas fa-info-circle me-2"></i>How to Use</h4>
                                    <ol class="usage-steps">
                                        <li>
                                            <i class="fas fa-mobile-alt me-2"></i>
                                            Open your mobile device's camera or QR scanner app
                                        </li>
                                        <li>
                                            <i class="fas fa-camera me-2"></i>
                                            Point the camera at the QR code displayed on the left
                                        </li>
                                        <li>
                                            <i class="fas fa-touch me-2"></i>
                                            Tap the notification or link that appears
                                        </li>
                                        <li>
                                            <i class="fas fa-search me-2"></i>
                                            You'll be redirected to the search interface
                                        </li>
                                    </ol>
                                    
                                    <div class="qr-features">
                                        <h5><i class="fas fa-star me-2"></i>Features</h5>
                                        <ul class="feature-list">
                                            <li><i class="fas fa-check text-success me-2"></i>Direct access to search</li>
                                            <li><i class="fas fa-check text-success me-2"></i>No login required for scanning</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Works with any QR scanner</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Updates automatically with Excel uploads</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Access Section -->
        <div class="row mt-4">
            <div class="col-lg-8 mx-auto">
                <div class="quick-access-card">
                    <h4><i class="fas fa-bolt me-2"></i>Quick Access</h4>
                    <p>Don't have a QR scanner? Use these direct links:</p>
                    <div class="quick-links">
                        <a href="{{ url_for('global_search') }}" class="quick-link">
                            <i class="fas fa-search"></i>
                            <span>Search Files</span>
                        </a>
                        <a href="{{ url_for('bundle_list') }}" class="quick-link">
                            <i class="fas fa-archive"></i>
                            <span>Browse Bundles</span>
                        </a>
                        <a href="{{ url_for('scan_qrcode') }}" class="quick-link">
                            <i class="fas fa-qrcode"></i>
                            <span>QR Scanner</span>
                        </a>
                        <a href="{{ url_for('dashboard') }}" class="quick-link">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.qr-search-page {
    background: #f8f9fa;
    min-height: calc(100vh - 200px);
}

.page-header {
    background: white;
    border-bottom: 1px solid #dee2e6;
    padding: 2rem 0;
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.page-subtitle {
    color: #6c757d;
    margin: 0.5rem 0 0 0;
}

.qr-search-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 2rem;
    text-align: center;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.card-subtitle {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
}

.card-body {
    padding: 2rem;
}

.qr-display {
    text-align: center;
}

#qr-code-container {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-loading {
    text-align: center;
    color: #6c757d;
}

.qr-loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #007bff;
}

.qr-loading p {
    margin: 0;
    font-size: 1rem;
}

.qr-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.qr-info {
    padding-left: 2rem;
}

.usage-steps {
    padding-left: 0;
    list-style: none;
}

.usage-steps li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
    color: #495057;
}

.usage-steps li:last-child {
    border-bottom: none;
}

.qr-features {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.feature-list {
    list-style: none;
    padding-left: 0;
}

.feature-list li {
    padding: 0.5rem 0;
    color: #495057;
}

.quick-access-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.quick-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 1.5rem;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    min-width: 120px;
}

.quick-link:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
}

.quick-link i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .qr-info {
        padding-left: 0;
        margin-top: 2rem;
    }
    
    .quick-links {
        gap: 0.5rem;
    }
    
    .quick-link {
        min-width: 100px;
        padding: 0.75rem;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for the QRCode library to load
    setTimeout(() => {
        generateQRCode();
    }, 500);
});

// Fallback: try to generate QR code when library loads
window.addEventListener('load', function() {
    if (typeof QRCode !== 'undefined') {
        generateQRCode();
    }
});

function generateQRCode() {
    const searchUrl = window.location.origin + "{{ url_for('global_search') }}";
    const qrContainer = document.getElementById('qr-code-container');

    // Show loading state
    qrContainer.innerHTML = `
        <div class="qr-loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Generating QR Code...</p>
        </div>
    `;

    // Check if QRCode library is loaded
    if (typeof QRCode === 'undefined') {
        console.error('QRCode library not loaded');
        qrContainer.innerHTML = `
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>QR Code library failed to load</p>
                <small>Please refresh the page</small>
            </div>
        `;
        return;
    }

    try {
        // Generate QR code
        QRCode.toCanvas(searchUrl, {
            width: 250,
            height: 250,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            },
            margin: 2,
            errorCorrectionLevel: 'M'
        }, function (error, canvas) {
            if (error) {
                console.error('QR Code generation failed:', error);
                qrContainer.innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>Failed to generate QR code</p>
                        <small>${error.message}</small>
                    </div>
                `;
            } else {
                // Clear loading and add canvas
                qrContainer.innerHTML = '';
                qrContainer.appendChild(canvas);
                console.log('QR Code generated successfully');
            }
        });
    } catch (error) {
        console.error('QR Code generation error:', error);
        qrContainer.innerHTML = `
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <p>Error generating QR code</p>
                <small>${error.message}</small>
            </div>
        `;
    }
}

function downloadQR() {
    const canvas = document.querySelector('#qr-code-container canvas');
    if (canvas) {
        const link = document.createElement('a');
        link.download = 'search-interface-qr.png';
        link.href = canvas.toDataURL();
        link.click();
    }
}

function printQR() {
    const canvas = document.querySelector('#qr-code-container canvas');
    if (canvas) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>Search Interface QR Code</title>
                    <style>
                        body { text-align: center; font-family: Arial, sans-serif; padding: 20px; }
                        img { max-width: 400px; margin: 20px; }
                        h1 { color: #333; }
                    </style>
                </head>
                <body>
                    <h1>Search Interface QR Code</h1>
                    <p>Scan to access the file search interface</p>
                    <img src="${canvas.toDataURL()}" alt="QR Code">
                    <p><small>Generated on ${new Date().toLocaleDateString()}</small></p>
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

function refreshQR() {
    generateQRCode();
}
</script>
{% endblock %}
