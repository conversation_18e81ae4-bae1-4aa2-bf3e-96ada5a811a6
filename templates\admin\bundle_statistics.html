<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bundle Statistics - T-Office Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .stats-container {
            padding: 2rem 0;
        }

        .stats-overview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .chart-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f8f9fa;
        }

        .section-icon {
            font-size: 1.5rem;
            margin-right: 0.5rem;
            color: #007bff;
        }

        .capacity-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .capacity-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            border-left: 4px solid #007bff;
        }

        .capacity-item.empty {
            border-left-color: #6c757d;
        }

        .capacity-item.active {
            border-left-color: #28a745;
        }

        .capacity-item.nearly-full {
            border-left-color: #ffc107;
        }

        .capacity-item.full {
            border-left-color: #dc3545;
        }

        .capacity-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .capacity-label {
            font-size: 0.8rem;
            color: #6c757d;
        }

        .capacity-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .capacity-fill {
            height: 100%;
            transition: width 0.3s ease;
        }

        .integrity-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .integrity-score {
            font-size: 3rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 1rem;
        }

        .score-excellent {
            color: #28a745;
        }

        .score-good {
            color: #17a2b8;
        }

        .score-warning {
            color: #ffc107;
        }

        .score-danger {
            color: #dc3545;
        }

        .assignment-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }

        .assignment-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .assignment-user {
            font-weight: bold;
            color: #007bff;
        }

        .assignment-time {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .compartment-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .compartment-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .compartment-title {
            font-weight: bold;
            color: #007bff;
        }

        .bundle-mini-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 0.5rem;
        }

        .bundle-mini {
            background: #e9ecef;
            border-radius: 4px;
            padding: 0.5rem;
            text-align: center;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .bundle-mini.empty {
            background: #6c757d;
            color: white;
        }

        .bundle-mini.active {
            background: #28a745;
            color: white;
        }

        .bundle-mini.nearly-full {
            background: #ffc107;
            color: #212529;
        }

        .bundle-mini.full {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('admin_bulk_upload') }}">
                <i class="fas fa-chart-bar me-2"></i>Bundle Statistics
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('bundle_list') }}">
                    <i class="fas fa-archive me-1"></i>Bundle List
                </a>
                <a class="nav-link" href="{{ url_for('admin_bulk_upload') }}">
                    <i class="fas fa-arrow-left me-1"></i>Admin Panel
                </a>
            </div>
        </div>
    </nav>

    <div class="container stats-container">
        <!-- Statistics Overview -->
        <div class="stats-overview">
            <h2 class="mb-3">
                <i class="fas fa-chart-bar me-2"></i>Bundle Management Statistics
            </h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ bundle_stats.total_bundles }}</div>
                    <div class="stat-label">Total Bundles</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ bundle_stats.total_files }}</div>
                    <div class="stat-label">Total Files</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ bundle_stats.active_bundles }}</div>
                    <div class="stat-label">Active Bundles</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ bundle_stats.empty_bundles }}</div>
                    <div class="stat-label">Empty Bundles</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ "%.1f"|format(bundle_stats.avg_capacity_usage) }}%</div>
                    <div class="stat-label">Avg Capacity Usage</div>
                </div>
            </div>
        </div>

        <!-- Data Integrity Report -->
        <div class="integrity-section">
            <div class="section-header">
                <i class="fas fa-shield-check section-icon"></i>
                <h3>Data Integrity Report</h3>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="integrity-score
                        {% if integrity_report.integrity_score >= 95 %}score-excellent
                        {% elif integrity_report.integrity_score >= 85 %}score-good
                        {% elif integrity_report.integrity_score >= 70 %}score-warning
                        {% else %}score-danger{% endif %}">
                        {{ integrity_report.integrity_score }}%
                    </div>
                    <div class="text-center">
                        <strong>Integrity Score</strong>
                    </div>
                </div>
                <div class="col-md-8">
                    <h5>Issues Found: {{ integrity_report.total_issues }}</h5>
                    {% if integrity_report.issues %}
                    <ul class="list-group">
                        {% for issue in integrity_report.issues[:5] %}
                        <li class="list-group-item">{{ issue }}</li>
                        {% endfor %}
                        {% if integrity_report.issues|length > 5 %}
                        <li class="list-group-item text-muted">
                            ... and {{ integrity_report.issues|length - 5 }} more issues
                        </li>
                        {% endif %}
                    </ul>
                    {% else %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        No data integrity issues found!
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Capacity Utilization Chart -->
        <div class="chart-section">
            <div class="section-header">
                <i class="fas fa-chart-pie section-icon"></i>
                <h3>Capacity Utilization</h3>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <canvas id="capacityChart" width="400" height="400"></canvas>
                </div>
                <div class="col-md-6">
                    <div class="capacity-grid">
                        {% for item in capacity_utilization[:20] %}
                        <div class="capacity-item {{ item.status }}">
                            <div class="capacity-number">{{ item.bundle_number }}</div>
                            <div class="capacity-label">{{ "%.0f"|format(item.utilization) }}%</div>
                            <div class="capacity-bar">
                                <div class="capacity-fill {{ item.status }}"
                                     style="width: {{ item.utilization }}%;
                                            background: {% if item.status == 'empty' %}#6c757d
                                                       {% elif item.status == 'active' %}#28a745
                                                       {% elif item.status == 'nearly_full' %}#ffc107
                                                       {% else %}#dc3545{% endif %}"></div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Compartment Distribution -->
        <div class="chart-section">
            <div class="section-header">
                <i class="fas fa-warehouse section-icon"></i>
                <h3>Compartment Distribution</h3>
            </div>

            {% for comp_num, comp_data in compartment_distribution.items() %}
            <div class="compartment-card">
                <div class="compartment-header">
                    <div class="compartment-title">
                        <i class="fas fa-box me-1"></i>
                        Compartment {{ comp_data.compartment_number }}
                        {% if comp_data.is_active %}
                        <span class="badge bg-success ms-2">Active</span>
                        {% else %}
                        <span class="badge bg-secondary ms-2">Available</span>
                        {% endif %}
                    </div>
                    <div>
                        {% if comp_data.is_active %}
                        <span class="badge bg-primary">{{ comp_data.total_bundles }} bundles</span>
                        <span class="badge bg-success">{{ comp_data.total_files }} files</span>
                        {% else %}
                        <span class="badge bg-secondary">No bundles assigned</span>
                        {% endif %}
                    </div>
                </div>

                <div class="mb-2">
                    <small class="text-muted">{{ comp_data.bundle_range }}</small>
                    {% if comp_data.original_range %}
                    <br><small class="text-muted">QR Range: {{ comp_data.original_range }}</small>
                    {% endif %}
                </div>

                {% if comp_data.bundles %}
                <div class="bundle-mini-grid">
                    {% for bundle in comp_data.bundles %}
                    <div class="bundle-mini {{ bundle.status }}"
                         title="Bundle {{ bundle.bundle_number }}: {{ bundle.file_count }}/{{ bundle.capacity }} files">
                        {{ bundle.bundle_number }}
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-info-circle me-1"></i>
                    No bundles currently assigned to this compartment
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <!-- Recent Bundle Assignments -->
        <div class="chart-section">
            <div class="section-header">
                <i class="fas fa-history section-icon"></i>
                <h3>Recent Bundle Assignments</h3>
            </div>

            {% if recent_assignments %}
            {% for assignment in recent_assignments %}
            <div class="assignment-item">
                <div class="assignment-header">
                    <div class="assignment-user">
                        {{ assignment.assigned_by_user.username if assignment.assigned_by_user else 'System' }}
                    </div>
                    <div class="assignment-time">
                        {{ assignment.assigned_at.strftime('%Y-%m-%d %H:%M:%S') }}
                    </div>
                </div>
                <div class="assignment-details">
                    <strong>File {{ assignment.file_id }}</strong>
                    {% if assignment.previous_bundle %}
                    moved from Bundle {{ assignment.previous_bundle.bundle_number }} to
                    {% else %}
                    assigned to
                    {% endif %}
                    <strong>Bundle {{ assignment.bundle.bundle_number }}</strong>
                    <br>
                    <small class="text-muted">
                        Type: {{ assignment.assignment_type.title() }}
                        {% if assignment.assignment_reason %}
                        | Reason: {{ assignment.assignment_reason }}
                        {% endif %}
                    </small>
                </div>
            </div>
            {% endfor %}
            {% else %}
            <div class="text-center text-muted py-4">
                <i class="fas fa-info-circle fa-2x mb-2"></i>
                <p>No recent bundle assignments</p>
            </div>
            {% endif %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Capacity Utilization Pie Chart
        const ctx = document.getElementById('capacityChart').getContext('2d');
        const capacityChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Empty', 'Active', 'Nearly Full', 'Full'],
                datasets: [{
                    data: [
                        {{ bundle_stats.empty_bundles }},
                        {{ bundle_stats.active_bundles }},
                        {{ bundle_stats.bundles_by_status.nearly_full|length }},
                        {{ bundle_stats.full_bundles }}
                    ],
                    backgroundColor: [
                        '#6c757d',
                        '#28a745',
                        '#ffc107',
                        '#dc3545'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} bundles (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Auto-refresh every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
