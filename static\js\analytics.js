// T-Office Analytics Module
class TOfficeAnalytics {
    constructor() {
        this.events = [];
        this.sessionId = this.generateSessionId();
        this.userId = null;
        this.isOnline = navigator.onLine;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startSession();
        this.setupPerformanceTracking();
        this.setupErrorTracking();
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    setupEventListeners() {
        // Track page views
        this.trackPageView();

        // Track clicks
        document.addEventListener('click', (e) => {
            this.trackClick(e);
        });

        // Track form submissions
        document.addEventListener('submit', (e) => {
            this.trackFormSubmission(e);
        });

        // Track file downloads
        document.addEventListener('click', (e) => {
            if (e.target.href && (e.target.href.includes('/download') || e.target.download)) {
                this.trackFileDownload(e.target.href);
            }
        });

        // Track search queries
        const searchInput = document.getElementById('globalSearch');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (e.target.value.length > 2) {
                        this.trackSearch(e.target.value);
                    }
                }, 1000);
            });
        }

        // Track network status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.trackEvent('network_status', { status: 'online' });
            this.syncOfflineEvents();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.trackEvent('network_status', { status: 'offline' });
        });

        // Track page visibility
        document.addEventListener('visibilitychange', () => {
            this.trackEvent('page_visibility', {
                hidden: document.hidden,
                visibilityState: document.visibilityState
            });
        });

        // Track before unload
        window.addEventListener('beforeunload', () => {
            this.endSession();
        });
    }

    startSession() {
        this.trackEvent('session_start', {
            sessionId: this.sessionId,
            userAgent: navigator.userAgent,
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth
            },
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            language: navigator.language
        });
    }

    endSession() {
        const sessionDuration = Date.now() - parseInt(this.sessionId.split('_')[1]);
        this.trackEvent('session_end', {
            sessionId: this.sessionId,
            duration: sessionDuration
        });
        this.sendEvents();
    }

    trackPageView() {
        this.trackEvent('page_view', {
            url: window.location.href,
            path: window.location.pathname,
            title: document.title,
            referrer: document.referrer
        });
    }

    trackClick(event) {
        const element = event.target;
        const data = {
            tagName: element.tagName,
            className: element.className,
            id: element.id,
            text: element.textContent?.substring(0, 100),
            href: element.href,
            x: event.clientX,
            y: event.clientY
        };

        // Track specific UI elements
        if (element.classList.contains('nav-link')) {
            data.type = 'navigation';
        } else if (element.classList.contains('btn') || element.type === 'button') {
            data.type = 'button';
        } else if (element.tagName === 'A') {
            data.type = 'link';
        }

        this.trackEvent('click', data);
    }

    trackFormSubmission(event) {
        const form = event.target;
        this.trackEvent('form_submit', {
            formId: form.id,
            formClass: form.className,
            action: form.action,
            method: form.method,
            fieldCount: form.elements.length
        });
    }

    trackFileDownload(url) {
        this.trackEvent('file_download', {
            url: url,
            filename: url.split('/').pop()
        });
    }

    trackSearch(query) {
        this.trackEvent('search', {
            query: query,
            queryLength: query.length
        });
    }

    trackFileAccess(fileId, fileName, action) {
        this.trackEvent('file_access', {
            fileId: fileId,
            fileName: fileName,
            action: action
        });
    }

    trackQRScan(fileId, method = 'camera') {
        this.trackEvent('qr_scan', {
            fileId: fileId,
            method: method
        });
    }

    trackVoiceCommand(command, success = true) {
        this.trackEvent('voice_command', {
            command: command,
            success: success
        });
    }

    trackError(error, context = {}) {
        this.trackEvent('error', {
            message: error.message,
            stack: error.stack,
            context: context,
            url: window.location.href
        });
    }

    trackPerformance(metric, value, context = {}) {
        this.trackEvent('performance', {
            metric: metric,
            value: value,
            context: context
        });
    }

    trackEvent(eventName, data = {}) {
        const event = {
            id: this.generateEventId(),
            name: eventName,
            data: data,
            timestamp: new Date().toISOString(),
            sessionId: this.sessionId,
            userId: this.userId,
            url: window.location.href,
            userAgent: navigator.userAgent
        };

        this.events.push(event);

        // Send events in batches or when offline
        if (this.events.length >= 10 || !this.isOnline) {
            this.sendEvents();
        }

        // Emit to Socket.IO if available
        if (typeof io !== 'undefined' && window.socket) {
            window.socket.emit('analytics_event', event);
        }

        console.log('Analytics Event:', eventName, data);
    }

    generateEventId() {
        return 'event_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    async sendEvents() {
        if (this.events.length === 0) return;

        const eventsToSend = [...this.events];
        this.events = [];

        if (this.isOnline) {
            try {
                const response = await fetch('/api/analytics', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        events: eventsToSend
                    })
                });

                if (!response.ok) {
                    throw new Error('Analytics send failed');
                }

                console.log('Analytics events sent successfully');
            } catch (error) {
                console.error('Failed to send analytics events:', error);
                // Store in localStorage for retry
                this.storeOfflineEvents(eventsToSend);
            }
        } else {
            // Store offline for later sync
            this.storeOfflineEvents(eventsToSend);
        }
    }

    storeOfflineEvents(events) {
        try {
            const stored = JSON.parse(localStorage.getItem('toffice_offline_analytics') || '[]');
            stored.push(...events);
            localStorage.setItem('toffice_offline_analytics', JSON.stringify(stored));
        } catch (error) {
            console.error('Failed to store offline analytics:', error);
        }
    }

    async syncOfflineEvents() {
        try {
            const stored = JSON.parse(localStorage.getItem('toffice_offline_analytics') || '[]');
            if (stored.length === 0) return;

            const response = await fetch('/api/analytics', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    events: stored
                })
            });

            if (response.ok) {
                localStorage.removeItem('toffice_offline_analytics');
                console.log('Offline analytics events synced successfully');
            }
        } catch (error) {
            console.error('Failed to sync offline analytics:', error);
        }
    }

    setupPerformanceTracking() {
        // Track page load performance
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    this.trackPerformance('page_load', {
                        loadTime: perfData.loadEventEnd - perfData.loadEventStart,
                        domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                        firstPaint: this.getFirstPaint(),
                        firstContentfulPaint: this.getFirstContentfulPaint()
                    });
                }
            }, 1000);
        });
    }

    getFirstPaint() {
        const paintEntries = performance.getEntriesByType('paint');
        const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
        return firstPaint ? firstPaint.startTime : null;
    }

    getFirstContentfulPaint() {
        const paintEntries = performance.getEntriesByType('paint');
        const firstContentfulPaint = paintEntries.find(entry => entry.name === 'first-contentful-paint');
        return firstContentfulPaint ? firstContentfulPaint.startTime : null;
    }

    setupErrorTracking() {
        // Track JavaScript errors
        window.addEventListener('error', (event) => {
            this.trackError(event.error || new Error(event.message), {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });

        // Track unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.trackError(new Error(event.reason), {
                type: 'unhandled_promise_rejection'
            });
        });
    }

    setUserId(userId) {
        this.userId = userId;
    }

    // Public API methods
    track(eventName, data) {
        this.trackEvent(eventName, data);
    }

    identify(userId, traits = {}) {
        this.setUserId(userId);
        this.trackEvent('identify', {
            userId: userId,
            traits: traits
        });
    }

    page(name, properties = {}) {
        this.trackEvent('page', {
            name: name,
            properties: properties
        });
    }
}

// Initialize analytics
const analytics = new TOfficeAnalytics();

// Export for global use
window.TOfficeAnalytics = analytics;

// Auto-identify user if available
document.addEventListener('DOMContentLoaded', () => {
    const userElement = document.querySelector('[data-user-id]');
    if (userElement) {
        analytics.identify(userElement.dataset.userId, {
            username: userElement.dataset.username
        });
    }
});

console.log('T-Office Analytics initialized');