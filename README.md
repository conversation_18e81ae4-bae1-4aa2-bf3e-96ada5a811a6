# 🏛️ Taluk Office Digital File Management System (toffice_proj)

A comprehensive, government-grade digital file management system designed specifically for Taluk Offices in Karnataka, India. This advanced system provides modern file tracking, QR code-based document management, compartment-based file organization, and comprehensive administrative tools with enhanced bundle distribution capabilities.

## 🆕 Recent Updates (January 2025)
- ✅ **Project Cleanup**: Removed test files, temporary QR codes, and development artifacts
- ✅ **Enhanced .gitignore**: Improved file exclusion patterns for cleaner repository
- ✅ **Repository Migration**: Moved to new repository structure for better organization
- ✅ **Documentation Updates**: Updated all repository links and installation instructions
- ✅ **Code Optimization**: Cleaned up codebase for production deployment

## 🌟 Complete Feature Overview

### 📦 **NEW: Advanced Compartment & Bundle Management System**
- **Compartment QR Codes** - Two-compartment system (Compartment 1: Bundles 1-400, Compartment 2: Bundles 401-800)
- **Bundle Drill-Down Navigation** - Clickable bundle numbers for direct file access
- **Automatic Bundle Assignment** - Files automatically distributed to correct compartments during Excel upload
- **Bundle-Specific File Listings** - View files organized by specific bundle numbers
- **QR Code Generation** - 800+ bundle QR codes and thousands of file QR codes
- **Real-time Search** - Search within specific bundles and compartments

### 📋 **Enhanced Core Functionality**
- **Digital File Tracking** - Advanced QR code-based document identification and tracking
- **Smart Search System** - Multi-level search by reference number, date, location, bundle, or keywords
- **Secure Access Control** - Enhanced role-based permissions with audit logging
- **Digital Archive** - Comprehensive document storage with compartment-based organization
- **Bulk Data Management** - Excel import with automatic compartment assignment and validation
- **Workflow Management** - Document approval and routing system with bundle tracking
- **Compliance & Reporting** - Generate detailed audit trails and compliance reports

### 🔐 **Enhanced User Roles & Security**
- **Administrator** - Full system access, bulk upload, compartment management, and user administration
- **Officer** - Document management, bundle access, and approval workflows
- **Clerk** - Basic document entry, retrieval, and limited bundle access
- **Bulk Data Access Control** - Session-based temporary access with 30-minute expiry
- **Audit Logging** - Complete tracking of all file and compartment access
- **Data Encryption** - Field-level encryption for sensitive information

### 🎨 **Professional UI with Enhanced Navigation**
- Government of Karnataka branding with modern design
- Mobile-responsive design with touch-friendly interfaces
- Professional color scheme with accessibility compliance
- Compartment-based navigation with visual feedback
- Bundle drill-down interface with breadcrumb navigation
- Real-time search with instant results

## 🚀 Quick Start

### Prerequisites
- **Python 3.8+** (Recommended: Python 3.8-3.11)
- **MySQL 8.0+** (Required for production, recommended for development)
- **Modern web browser** (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)
- **Git** for cloning the repository

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Shreyanka-A-Y/TO-project.git
   cd TO-project
   ```

2. **Create and activate virtual environment**
   ```bash
   # Create virtual environment
   python -m venv venv

   # Activate virtual environment
   # On Windows:
   venv\Scripts\activate
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Database Setup (MySQL Required)**

   **Step 1: Install and Start MySQL**
   ```bash
   # On macOS with Homebrew:
   brew install mysql
   brew services start mysql

   # On Ubuntu/Debian:
   sudo apt update
   sudo apt install mysql-server
   sudo systemctl start mysql

   # On Windows: Download and install MySQL from official website
   ```

   **Step 2: Create Database and User**
   ```bash
   # Run the automated setup script
   python setup_mysql.py

   # Or manually create database:
   mysql -u root -p
   CREATE DATABASE toffice_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'toffice_user'@'localhost' IDENTIFIED BY 'secure_password_123';
   GRANT ALL PRIVILEGES ON toffice_db.* TO 'toffice_user'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```

   **Step 3: Configure Database Connection**
   ```bash
   # Create .env file in project root
   echo "DATABASE_URL=mysql+pymysql://toffice_user:secure_password_123@localhost/toffice_db" > .env
   echo "SECRET_KEY=your-super-secret-key-here" >> .env
   ```

5. **Initialize Database Schema**
   ```bash
   # Run database initialization
   python -c "from app import app, db; app.app_context().push(); db.create_all(); print('Database initialized successfully!')"
   ```

6. **Generate Compartment QR Codes (Required)**
   ```bash
   # Generate QR codes for compartments and bundles
   python commands.py generate-compartment-qrs
   ```

7. **Run the application**
   ```bash
   python app.py
   ```

8. **Access the system**
   - Open: http://127.0.0.1:5001
   - Login with default credentials (see below)

## 👥 Default Login Credentials

| Role | Username | Password | Description |
|------|----------|----------|-------------|
| Administrator | `admin` | `admin123` | Full system access, bulk upload, compartment management |
| Officer | `officer` | `officer123` | Document management, bundle access, approval workflows |
| Clerk | `clerk` | `clerk123` | Basic operations, limited bundle access |

⚠️ **Important**: Change these passwords in production!

## 🗄️ Database Schema & Configuration

### MySQL Database Requirements

**Minimum Requirements:**
- MySQL 8.0+ or MariaDB 10.5+
- InnoDB storage engine (default)
- UTF8MB4 character set support
- At least 1GB available storage space
- MySQL user with CREATE, ALTER, INSERT, UPDATE, DELETE, SELECT privileges

### Database Tables Structure

The system uses the following main tables:

#### Core Tables
- **`users`** - User accounts and authentication
- **`files`** - File records with metadata and Excel import data
- **`locations`** - Geographic location data (districts, taluks, hoblis, villages)
- **`bundles`** - Bundle management (800 bundles across 2 compartments)
- **`compartment_qr`** - Compartment QR code management

#### Security & Audit Tables
- **`access_logs`** - Complete audit trail of all system access
- **`bulk_data_access_sessions`** - Temporary access sessions for bulk data
- **`user_sessions`** - Active user session management

#### Key Database Features
- **Automatic Indexing** - Optimized queries for large datasets
- **Foreign Key Constraints** - Data integrity enforcement
- **JSON Data Storage** - Excel import data stored as JSON in `excel_row_data` field
- **Audit Timestamps** - Created/updated timestamps on all records
- **Soft Deletes** - Data preservation with deletion flags

### Database Configuration Options

#### Development Configuration
```python
# config.py - Development
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://toffice_user:password@localhost/toffice_dev'
SQLALCHEMY_TRACK_MODIFICATIONS = False
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
}
```

#### Production Configuration
```python
# config.py - Production
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://toffice_user:secure_password@localhost/toffice_prod'
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_size': 20,
    'max_overflow': 30,
    'pool_pre_ping': True,
    'pool_recycle': 3600,
}
```

### Database Backup & Maintenance

#### Daily Backup Script
```bash
#!/bin/bash
# backup_database.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u toffice_user -p toffice_db > backup_${DATE}.sql
gzip backup_${DATE}.sql
```

#### Database Optimization
```sql
-- Run monthly for optimization
OPTIMIZE TABLE files, bundles, access_logs;
ANALYZE TABLE files, bundles, access_logs;
```

## 📁 Enhanced Project Structure

```
toffice_proj/
├── 📄 Core Application Files
│   ├── app.py                          # Main Flask application with enhanced routes
│   ├── config.py                       # Configuration settings and database config
│   ├── extensions.py                   # Flask extensions initialization
│   ├── commands.py                     # CLI commands for QR generation
│   └── requirements.txt                # Python dependencies
│
├── 🗄️ Database & Setup
│   ├── setup_mysql.py                  # Automated MySQL setup script
│   ├── create_mysql_db.py              # Database creation utility
│   ├── reset_mysql_db.py               # Database reset utility
│   ├── fix_database_schema.py          # Schema migration script
│   └── create_database.sql             # SQL schema definition
│
├── 🏗️ Models (Database Schema)
│   ├── models/
│   │   ├── user.py                     # User authentication and roles
│   │   ├── file.py                     # File records and CompartmentQR model
│   │   ├── location.py                 # Geographic location data
│   │   ├── access_log.py               # Audit logging system
│   │   ├── bundle.py                   # Bundle management (800 bundles)
│   │   └── bulk_data_security.py       # Bulk data access control
│
├── 🎨 Templates (Enhanced UI)
│   ├── templates/
│   │   ├── base.html                   # Base template with navigation
│   │   ├── dashboard.html              # Enhanced dashboard with compartment access
│   │   ├── login.html                  # User authentication
│   │   ├── 📦 Compartment & Bundle Templates
│   │   │   ├── compartment_qr.html     # Compartment QR management
│   │   │   ├── view_compartment_qr.html # Compartment view with clickable bundles
│   │   │   ├── compartment_bundle_files.html # Bundle-specific file listings
│   │   │   ├── compartment_bundles.html # Compartment file overview
│   │   │   └── compartment_search.html # Compartment search interface
│   │   ├── 📋 Admin & Upload Templates
│   │   │   ├── admin_bulk_upload.html  # Enhanced bulk upload with compartment assignment
│   │   │   ├── bulk_data_unlock.html   # Bulk data access control
│   │   │   └── admin/                  # Admin-specific templates
│   │   ├── 📄 File Management Templates
│   │   │   ├── add_file.html           # File creation form
│   │   │   ├── view_file.html          # File details view
│   │   │   ├── upload_excel.html       # Excel upload interface
│   │   │   └── scan.html               # QR code scanning interface
│   │   └── 📊 Analytics & Reports
│   │       ├── analytics.html          # System analytics
│   │       └── collaboration.html      # Collaboration features
│
├── 🎯 Static Assets
│   ├── static/
│   │   ├── css/                        # Stylesheets with enhanced UI
│   │   ├── js/                         # JavaScript for interactive features
│   │   ├── images/                     # Application images and icons
│   │   ├── qrcodes/                    # Generated QR codes (800+ bundle QRs)
│   │   │   ├── compartment_qr_1.png    # Compartment 1 QR code
│   │   │   ├── compartment_qr_2.png    # Compartment 2 QR code
│   │   │   ├── bundle_qr_001.png       # Bundle QR codes (1-800)
│   │   │   └── file_qr_*.png           # Individual file QR codes
│   │   └── uploads/                    # Uploaded Excel files
│
├── 🔧 Utilities & Helpers
│   ├── utils/
│   │   ├── qr_generator.py             # Enhanced QR code generation
│   │   ├── bundle_manager.py           # Bundle and compartment management
│   │   ├── security.py                 # Security utilities and audit logging
│   │   ├── file_tracker.py             # File tracking utilities
│   │   └── ar_locator.py               # Location-based services
│
├── 🧪 Testing Framework
│   ├── test_enhanced_compartment_assignment.py    # Comprehensive compartment tests
│   ├── test_compartment_bundle_drill_down.py      # Bundle navigation tests
│   ├── test_bulk_upload.py                        # Bulk upload testing
│   ├── test_qr_scanning.py                        # QR code functionality tests
│   ├── verify_compartment_assignment_integration.py # Integration verification
│   ├── verify_compartment_qr_routes.py            # Route verification
│   └── test_*.xlsx                                 # Test data files
│
├── 📚 Documentation
│   ├── README.md                                   # This comprehensive guide
│   ├── ENHANCED_COMPARTMENT_ASSIGNMENT_IMPLEMENTATION.md
│   ├── COMPARTMENT_QR_BUNDLE_DRILL_DOWN.md
│   ├── COMPARTMENT_QR_IMPLEMENTATION_SUMMARY.md
│   ├── MYSQL_SETUP_COMPLETE.md
│   └── DATABASE_SCHEMA_FIX.md
│
└── 🔒 Environment & Config
    ├── .env                            # Environment variables (create manually)
    ├── venv/                           # Virtual environment
    └── instance/                       # Instance-specific files
```

## 🔧 Enhanced Configuration

### Environment Variables (.env)
Create a `.env` file in the project root with the following configuration:

```env
# Database Configuration (Required)
DATABASE_URL=mysql+pymysql://toffice_user:secure_password_123@localhost/toffice_db

# Security Configuration (Required)
SECRET_KEY=your-super-secret-key-change-this-in-production

# Application Configuration (Optional)
FLASK_ENV=development
FLASK_DEBUG=True
UPLOAD_FOLDER=static/uploads
QR_CODE_FOLDER=static/qrcodes
MAX_CONTENT_LENGTH=16777216

# MySQL Connection Pool Settings (Production)
SQLALCHEMY_POOL_SIZE=20
SQLALCHEMY_MAX_OVERFLOW=30
SQLALCHEMY_POOL_RECYCLE=3600
```

### Database Configuration Options

#### Development Environment
```python
# For local development with MySQL
DATABASE_URL=mysql+pymysql://toffice_user:password@localhost/toffice_dev
FLASK_ENV=development
FLASK_DEBUG=True
```

#### Production Environment
```python
# For production deployment
DATABASE_URL=mysql+pymysql://toffice_user:secure_password@production_host/toffice_prod
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=generate-a-strong-secret-key-here
```

#### Docker Environment
```python
# For Docker deployment
DATABASE_URL=mysql+pymysql://toffice_user:password@mysql_container:3306/toffice_db
FLASK_ENV=production
```

## 📱 Comprehensive Features Overview

### 🏠 Enhanced Dashboard
- **Real-time Statistics** - File counts, compartment utilization, bundle statistics
- **Recent Activity** - Latest file uploads, compartment access, bundle operations
- **Quick Action Buttons** - Direct access to compartment QR, bulk upload, file management
- **Role-specific Views** - Customized interface based on user permissions
- **Compartment Quick Access** - Dropdown menu for direct compartment navigation

### 📦 **NEW: Compartment & Bundle Management**
- **Two-Compartment System** - Compartment 1 (Bundles 1-400), Compartment 2 (Bundles 401-800)
- **Clickable Bundle Navigation** - Click any bundle number to view contained files
- **Bundle-Specific File Listings** - Detailed view of files within specific bundles
- **Automatic Assignment** - Files automatically assigned to correct compartments during upload
- **QR Code Integration** - 800+ bundle QR codes for physical tracking
- **Real-time Search** - Search within specific bundles and compartments

### 📄 Enhanced Document Management
- **Advanced File Upload** - Single file upload with comprehensive metadata
- **Bulk Excel Import** - Upload thousands of files via Excel with automatic compartment assignment
- **QR Code Generation** - Automatic QR codes for files and bundles
- **Location Tracking** - Physical location with rack, row, and position information
- **Bundle Assignment** - Automatic assignment to bundles based on BundleNo in Excel data
- **Access History** - Complete audit trail of all file access

### 🔍 Advanced QR Code System
- **Multi-level QR Codes** - File QR codes, Bundle QR codes, Compartment QR codes
- **Camera-based Scanning** - Mobile-optimized QR code scanner
- **Manual QR Input** - Text-based QR code entry for accessibility
- **Instant Retrieval** - Direct access to files, bundles, or compartments
- **Drill-down Navigation** - From compartment → bundle → specific files

### 📊 **NEW: Enhanced Bulk Data Management**
- **Excel Import Validation** - Comprehensive validation of 15-column Excel format
- **Automatic Compartment Assignment** - Real-time assignment during upload
- **100% Data Preservation** - No data loss during import process
- **Assignment Statistics** - Real-time feedback on compartment distribution
- **Quick Access Links** - Direct navigation to uploaded files via compartment interface
- **Error Handling** - Graceful handling of invalid data with detailed feedback

### 🔍 Advanced Search & Navigation
- **Multi-level Search** - Search across files, bundles, compartments
- **Location-based Search** - Search by district, taluk, hobli, village
- **Bundle-specific Search** - Real-time search within individual bundles
- **Reference Number Search** - Quick access via RefID or FILE_NO
- **Date Range Filtering** - Search by creation date, closure date, receipt date
- **Survey Number Search** - Land record specific search capabilities

### 👥 Enhanced User Management & Security
- **Role-based Access Control** - Administrator, Officer, Clerk with specific permissions
- **Bulk Data Access Control** - Session-based temporary access with 30-minute expiry
- **Comprehensive Audit Logging** - Complete tracking of all system access
- **Data Encryption** - Field-level encryption for sensitive information
- **Session Management** - Secure session handling with automatic expiry
- **Permission Management** - Granular control over feature access

### 📊 Reports & Analytics
- **Compartment Utilization** - Real-time statistics on compartment and bundle usage
- **File Access Reports** - Detailed reports on file access patterns
- **User Activity Monitoring** - Complete audit trails for all user actions
- **Bulk Upload Statistics** - Detailed reports on Excel import success rates
- **Compliance Reports** - Government-ready audit and compliance documentation
- **Export Capabilities** - Export reports to Excel, PDF, and other formats

## 🛡️ Enhanced Security Features

### 🔐 Authentication & Authorization
- **Secure Password Hashing** - Werkzeug password hashing with salt
- **Role-based Access Control** - Three-tier permission system (Admin/Officer/Clerk)
- **Session Management** - Secure Flask-Login session handling with automatic expiry
- **Multi-factor Authentication Ready** - Framework prepared for MFA implementation

### 🔒 Data Protection & Encryption
- **Field-level Encryption** - Sensitive data encrypted using cryptography library
- **Data Masking** - Non-administrators see masked sensitive information
- **Secure File Storage** - Uploaded files stored with secure naming and access controls
- **Database Security** - Parameterized queries prevent SQL injection

### 📋 Comprehensive Audit System
- **Complete Activity Logging** - Every system action logged with timestamps
- **Bulk Data Access Control** - Session-based temporary access with 30-minute expiry
- **User Access Tracking** - Detailed logs of all user activities and file access
- **Compartment Access Logging** - Specific tracking of compartment and bundle access
- **IP Address Logging** - Client information captured for security analysis

### 🛡️ Advanced Security Controls
- **Bulk Data Unlock Workflow** - Secure process for accessing bulk imported data
- **Search-based Access Control** - Requires 2+ search criteria for bulk data access
- **Temporary Access Tokens** - Time-limited access to sensitive bulk data
- **Data Validation** - Comprehensive input validation and sanitization
- **CSRF Protection** - Cross-site request forgery protection enabled

## 📖 Comprehensive Usage Guide

### 🚀 Getting Started

#### 1. First-Time Setup
```bash
# After installation, access the application
http://127.0.0.1:5001

# Login with default admin credentials
Username: admin
Password: admin123

# Change default passwords immediately!
```

#### 2. Generate Required QR Codes
```bash
# Generate compartment and bundle QR codes (required for full functionality)
python commands.py generate-compartment-qrs

# Verify QR code generation
ls static/qrcodes/
# Should show: compartment_qr_1.png, compartment_qr_2.png, bundle_qr_*.png
```

### 📦 Compartment & Bundle Management

#### Accessing Compartment QR Interface
1. **From Dashboard**: Click "Compartment QR" dropdown → Select compartment
2. **Direct URL**:
   - Compartment 1: `http://127.0.0.1:5001/compartment-qr/1`
   - Compartment 2: `http://127.0.0.1:5001/compartment-qr/2`

#### Bundle Navigation
1. **View Compartment**: Click on compartment number (1 or 2)
2. **Select Bundle**: Click on any bundle number (1-400 for Compartment 1, 401-800 for Compartment 2)
3. **View Files**: See all files assigned to that specific bundle
4. **Search Within Bundle**: Use the search box to find specific files

#### Bundle-Specific URLs
```
# View files in specific bundles
http://127.0.0.1:5001/compartment-qr/1/bundle/150  # Bundle 150 in Compartment 1
http://127.0.0.1:5001/compartment-qr/2/bundle/650  # Bundle 650 in Compartment 2
```

### 📊 Bulk Excel Data Upload

#### Excel File Format (15 Columns Required)
```
SL No | INDEX ID | RefID | FILE_NO | Category | DisposalCat | Createddate |
RowNo | RackNo | RecordRoomSlNo | BundleNo | Subject | hobli_name | village_name | survey_no
```

#### Upload Process
1. **Access Admin Interface**: Dashboard → Admin → Bulk Upload
2. **Select Excel File**: Choose file with proper 15-column format
3. **Upload & Process**: System automatically:
   - Validates all data
   - Assigns files to compartments based on BundleNo
   - Preserves 100% of data (even invalid entries)
   - Provides detailed assignment statistics
4. **View Results**: Use quick access buttons to navigate to compartments

#### Automatic Compartment Assignment
- **BundleNo 1-400** → Automatically assigned to **Compartment 1**
- **BundleNo 401-800** → Automatically assigned to **Compartment 2**
- **Invalid BundleNo** → Preserved with warnings, not assigned to compartments

### 🔍 Search & Navigation

#### Multi-level Search Options
1. **Global Search**: Search across all files and bundles
2. **Compartment Search**: Search within specific compartment
3. **Bundle Search**: Search within specific bundle
4. **Location Search**: Search by geographic location (district/taluk/hobli/village)
5. **Reference Search**: Search by RefID, FILE_NO, or Index ID

#### Search Examples
```
# Search by Reference ID
RefID: REF001

# Search by location
District: Chikkamagaluru, Taluk: Kadur, Hobli: Aldur

# Search by survey number
Survey No: 123/4

# Search by date range
Created Date: 2024-01-01 to 2024-12-31
```

## 🧪 Comprehensive Testing Framework

### Test Scripts Available

#### 1. Enhanced Compartment Assignment Testing
```bash
# Create comprehensive test data with 20 files across all scenarios
python test_enhanced_compartment_assignment.py

# This creates test_enhanced_compartment_assignment.xlsx with:
# - 10 files for Compartment 1 (bundles 1-400)
# - 8 files for Compartment 2 (bundles 401-800)
# - 2 files with invalid bundle numbers for error testing
```

#### 2. Bundle Drill-Down Navigation Testing
```bash
# Test bundle-specific file listings and navigation
python test_compartment_bundle_drill_down.py

# Creates test data for specific bundle testing
```

#### 3. Integration Verification
```bash
# Verify all routes and functionality are working
python verify_compartment_assignment_integration.py
python verify_compartment_qr_routes.py
```

#### 4. QR Code Functionality Testing
```bash
# Test QR code generation and scanning
python test_qr_scanning.py
python test_qr_formats.py
```

### Manual Testing Checklist

#### ✅ Basic Functionality
- [ ] Application starts without errors
- [ ] Login with all three user roles works
- [ ] Dashboard displays correctly for each role
- [ ] File upload (single file) works
- [ ] QR code generation works

#### ✅ Compartment & Bundle Features
- [ ] Compartment QR interface loads
- [ ] Bundle numbers are clickable
- [ ] Bundle-specific file listings work
- [ ] Search within bundles functions
- [ ] Navigation breadcrumbs work

#### ✅ Bulk Upload Features
- [ ] Excel file upload works
- [ ] Automatic compartment assignment functions
- [ ] Upload summary displays correctly
- [ ] Quick access buttons work
- [ ] 100% data preservation verified

#### ✅ Security Features
- [ ] Role-based access control works
- [ ] Audit logging captures all actions
- [ ] Bulk data access control functions
- [ ] Session management works correctly

## 🔧 Troubleshooting Guide

### Common Issues & Solutions

#### 1. NameError: name 'current_app' is not defined
**Solution**: This has been fixed in the latest version. If you encounter this:
```bash
# Ensure you have the latest code
git pull origin main

# The fix includes proper import of current_app in app.py
# and using app.logger instead of current_app.logger in main app file
```

#### 2. Database Connection Issues
**Symptoms**: Application hangs on startup, database errors
**Solutions**:
```bash
# Check MySQL is running
sudo systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# Verify database exists
mysql -u toffice_user -p
SHOW DATABASES;

# Reset database if needed
python reset_mysql_db.py
```

#### 3. QR Codes Not Generating
**Symptoms**: Missing QR code images, 404 errors for QR codes
**Solutions**:
```bash
# Generate QR codes manually
python commands.py generate-compartment-qrs

# Check QR code directory
ls -la static/qrcodes/

# Verify permissions
chmod 755 static/qrcodes/
```

#### 4. Excel Upload Fails
**Symptoms**: Upload errors, validation failures
**Solutions**:
```bash
# Verify Excel file format (15 columns required)
# Check file size (max 16MB)
# Ensure BundleNo column contains valid numbers (1-800)

# Test with provided test file
python test_enhanced_compartment_assignment.py
# Upload the generated test_enhanced_compartment_assignment.xlsx
```

#### 5. Compartment Navigation Issues
**Symptoms**: Bundle numbers not clickable, 404 errors
**Solutions**:
```bash
# Verify routes are registered
python verify_compartment_qr_routes.py

# Check database has compartment records
python -c "from app import app, db; from models.file import CompartmentQR; app.app_context().push(); print(CompartmentQR.query.all())"
```

#### 6. Performance Issues
**Symptoms**: Slow loading, timeouts
**Solutions**:
```bash
# Optimize database
mysql -u toffice_user -p toffice_db
OPTIMIZE TABLE files, bundles, access_logs;

# Check database size
SELECT table_name, ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)' FROM information_schema.tables WHERE table_schema = 'toffice_db';

# Consider adding indexes for large datasets
```

### Environment-Specific Issues

#### Windows-Specific
```bash
# Use Windows path separators in .env
DATABASE_URL=mysql+pymysql://toffice_user:password@localhost/toffice_db

# Activate virtual environment
venv\Scripts\activate
```

#### macOS-Specific
```bash
# Install MySQL via Homebrew
brew install mysql
brew services start mysql

# Activate virtual environment
source venv/bin/activate
```

#### Linux-Specific
```bash
# Install MySQL
sudo apt update && sudo apt install mysql-server

# Start MySQL service
sudo systemctl start mysql
sudo systemctl enable mysql
```

## 🌐 Browser Support & Compatibility

### Fully Supported Browsers
- **Chrome 90+** - Full functionality including camera QR scanning
- **Firefox 88+** - Full functionality with camera access
- **Safari 14+** - Full functionality on macOS and iOS
- **Edge 90+** - Full functionality including PWA features

### Mobile Support
- **iOS Safari 14+** - Optimized touch interface, camera QR scanning
- **Chrome Mobile 90+** - Full functionality with responsive design
- **Samsung Internet 14+** - Compatible with all features
- **Firefox Mobile 88+** - Full functionality

### Feature Compatibility
| Feature | Chrome | Firefox | Safari | Edge | Mobile |
|---------|--------|---------|--------|------|--------|
| QR Scanning | ✅ | ✅ | ✅ | ✅ | ✅ |
| File Upload | ✅ | ✅ | ✅ | ✅ | ✅ |
| Responsive UI | ✅ | ✅ | ✅ | ✅ | ✅ |
| PWA Features | ✅ | ✅ | ✅ | ✅ | ✅ |

## 📞 Support & Documentation

### Technical Support
- **GitHub Repository**: https://github.com/Shreyanka-A-Y/TO-project
- **Issues & Bug Reports**: Use GitHub Issues for technical problems
- **Feature Requests**: Submit via GitHub Issues with enhancement label

### Documentation Resources
- **README.md** - This comprehensive guide
- **ENHANCED_COMPARTMENT_ASSIGNMENT_IMPLEMENTATION.md** - Detailed technical implementation
- **COMPARTMENT_QR_BUNDLE_DRILL_DOWN.md** - Bundle navigation documentation
- **MYSQL_SETUP_COMPLETE.md** - Database setup guide
- **Test Scripts** - Comprehensive testing documentation in test files

### Government Contact
For official government inquiries:
- **Department**: Karnataka Digital Services
- **Project**: Taluk Office Digitization Initiative
- **Classification**: Government of Karnataka Internal Project

## 📄 License & Compliance

### Software License
This software is developed for **Government of Karnataka** under the Digital India Initiative.
- **Ownership**: Government of Karnataka
- **Usage**: Restricted to authorized government personnel
- **Distribution**: Internal government use only
- **Modifications**: Require official approval

### Compliance Standards
- **Data Protection**: Compliant with Indian data protection regulations
- **Government Standards**: Follows Karnataka government IT policies
- **Security**: Meets government security requirements for sensitive data
- **Audit**: Full audit trail for compliance reporting

## 🤝 Contributing & Development

### For Government Developers
This is an active government project. For contributions:
1. **Contact**: IT Department, Government of Karnataka
2. **Process**: Follow government software development guidelines
3. **Testing**: All changes must pass comprehensive test suite
4. **Documentation**: Update documentation for any new features

### Development Guidelines
- **Code Quality**: Follow PEP 8 Python standards
- **Security**: All code must pass security review
- **Testing**: Minimum 90% test coverage required
- **Documentation**: Comprehensive documentation for all features

---

## 🏛️ Government of Karnataka - Digital India Initiative

**Project**: Taluk Office Digital File Management System
**Version**: Enhanced with Compartment & Bundle Management
**Status**: Production Ready
**Last Updated**: January 2025
**Repository**: https://github.com/Shreyanka-A-Y/TO-project

*Transforming governance through technology - Making government services more efficient, transparent, and accessible to citizens.*

### Key Achievements
- ✅ **800+ Bundle QR Codes** generated for physical file tracking
- ✅ **Two-Compartment System** for organized file management
- ✅ **100% Data Preservation** during bulk Excel imports
- ✅ **Comprehensive Security** with audit logging and access controls
- ✅ **Mobile-Responsive Design** for field access
- ✅ **Government-Grade Security** meeting all compliance requirements
