{% extends "base.html" %}

{% block title %}Compartment {{ compartment_number }} Files - Taluk Office{% endblock %}

{% block styles %}
<style>
.bundles-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.compartment-info {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
    border-radius: var(--radius-lg);
    margin-bottom: 1.5rem;
    text-align: center;
}

.compartment-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-weight: 600;
    display: inline-block;
    margin: 0 0.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: var(--radius-lg);
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.search-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.search-form {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.search-group {
    flex: 1;
    min-width: 200px;
}

.search-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    transition: var(--transition-normal);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.clear-btn {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.clear-btn:hover {
    background: var(--gray-200);
}

.files-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.files-header {
    background: var(--gradient-success);
    color: white;
    padding: 1.5rem 2rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.files-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
}

.files-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

.file-item {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition-fast);
    position: relative;
}

.file-item:hover {
    background: var(--gray-50);
}

.file-item:last-child {
    border-bottom: none;
}

.file-item.highlighted {
    background: var(--primary-bg);
    border-left: 4px solid var(--primary-color);
}

.file-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.file-title {
    font-weight: 600;
    color: var(--gray-800);
    font-size: 1.1rem;
    margin: 0;
    flex: 1;
}

.bundle-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.action-btn.primary {
    background: var(--primary-color);
    color: white;
}

.action-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.file-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-group {
    background: var(--gray-50);
    padding: 1rem;
    border-radius: var(--radius-lg);
}

.detail-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.detail-value {
    font-weight: 600;
    color: var(--gray-800);
}

.pagination-section {
    padding: 2rem;
    text-align: center;
    background: var(--gray-50);
}

.pagination {
    display: inline-flex;
    gap: 0.5rem;
    align-items: center;
}

.page-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--gray-700);
    border-radius: var(--radius-md);
    text-decoration: none;
    transition: var(--transition-fast);
}

.page-btn:hover {
    background: var(--gray-100);
    color: var(--gray-800);
}

.page-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.no-files {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--gray-500);
}

.no-files-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .search-form {
        flex-direction: column;
    }

    .search-group {
        min-width: auto;
    }

    .search-btn, .clear-btn {
        width: 100%;
        justify-content: center;
    }

    .file-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .file-details {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.search-hint {
    background: var(--info-bg);
    border: 1px solid var(--info-border);
    border-radius: var(--radius-lg);
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: var(--info-color);
}

.search-hint-icon {
    margin-right: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Header -->
    <div class="bundles-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="welcome-text">
                    <i class="fas fa-archive me-3"></i>Compartment {{ compartment_number }} Files
                </h1>
                <p class="welcome-subtitle">Browse all files in bundles {{ range_start }}-{{ range_end }}</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{{ url_for('dashboard') }}" class="quick-action-btn">
                    <i class="fas fa-arrow-left"></i>Back to Dashboard
                </a>
            </div>
        </div>

        <div class="compartment-info">
            <div>
                <i class="fas fa-archive me-2"></i>
                <span class="compartment-badge">Compartment {{ compartment_number }}</span>
                <span>Bundles {{ range_start }}-{{ range_end }}</span>
            </div>

            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ total_files }}</div>
                    <div class="stat-label">Total Files</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ total_bundles }}</div>
                    <div class="stat-label">Active Bundles</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ range_end - range_start + 1 }}</div>
                    <div class="stat-label">Bundle Range</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ files_pagination.pages }}</div>
                    <div class="stat-label">Pages</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="search-section">
        <div class="search-hint">
            <i class="fas fa-search search-hint-icon"></i>
            <strong>File Search:</strong> Enter a bundle number ({{ range_start }}-{{ range_end }}) or reference ID to find specific files.
        </div>

        <form method="GET" action="{{ url_for('compartment_bundles', compartment_number=compartment_number) }}">
            <div class="search-form">
                <div class="search-group">
                    <label class="search-label" for="ref_id_search">
                        <i class="fas fa-id-card me-1"></i>Reference ID
                    </label>
                    <input type="text"
                           id="ref_id_search"
                           name="ref_id"
                           class="search-input"
                           value="{{ search_ref_id or '' }}"
                           placeholder="Enter reference ID">
                </div>

                <div class="search-group">
                    <label class="search-label" for="bundle_search">
                        <i class="fas fa-hashtag me-1"></i>Bundle Number
                    </label>
                    <input type="number"
                           id="bundle_search"
                           name="bundle"
                           class="search-input"
                           value="{{ search_bundle or '' }}"
                           placeholder="Enter bundle number ({{ range_start }}-{{ range_end }})"
                           min="{{ range_start }}"
                           max="{{ range_end }}">
                </div>

                <div style="display: flex; gap: 0.5rem;">
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search"></i>Search Files
                    </button>
                    <a href="{{ url_for('compartment_bundles', compartment_number=compartment_number) }}"
                       class="search-btn clear-btn">
                        <i class="fas fa-times"></i>Show All
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Files Section -->
    <div class="files-section">
        <h2 class="files-header">
            <span>
                <i class="fas fa-file-alt me-2"></i>
                {% if search_ref_id and search_bundle %}
                    Files with RefID "{{ search_ref_id }}" in Bundle {{ search_bundle }}
                {% elif search_ref_id %}
                    Files with RefID "{{ search_ref_id }}"
                {% elif search_bundle %}
                    Files in Bundle {{ search_bundle }}
                {% else %}
                    All Files in Compartment {{ compartment_number }}
                {% endif %}
            </span>
            <span class="files-count">{{ files_pagination.total }} files</span>
        </h2>

        {% if files %}
        <!-- Bundle-organized view -->
        {% if files_by_bundle and not search_bundle %}
        <div class="bundle-sections">
            {% for bundle_num in files_by_bundle.keys() | sort %}
            <div class="bundle-section" style="margin-bottom: 2rem;">
                <div class="bundle-header" style="background: var(--primary-color); color: white; padding: 1rem 2rem; margin: 0; font-weight: 600; display: flex; align-items: center; justify-content: between;">
                    <span><i class="fas fa-layer-group me-2"></i>Bundle {{ bundle_num }}</span>
                    <span class="files-count">{{ files_by_bundle[bundle_num] | length }} files</span>
                </div>
                <ul class="files-list" style="margin: 0;">
                    {% for file_data in files_by_bundle[bundle_num] %}
                    <li class="file-item {% if search_ref_id and file_data.excel_data.get('RefID', '').lower() == search_ref_id.lower() %}highlighted{% endif %}">
                        <div class="file-header">
                            <h3 class="file-title">{{ file_data.file.title }}</h3>
                            <div style="display: flex; align-items: center; gap: 1rem;">
                                <div class="file-actions">
                                    <a href="{{ url_for('view_file', file_id=file_data.file.id) }}" class="action-btn primary">
                                        <i class="fas fa-eye"></i>View
                                    </a>
                                    {% if file_data.file.qr_code %}
                                    <a href="{{ url_for('get_qrcode', file_id=file_data.file.id) }}" class="action-btn secondary" target="_blank">
                                        <i class="fas fa-qrcode"></i>QR Code
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="file-details">
                            {% if file_data.excel_data.get('RefID') %}
                            <div class="detail-group">
                                <div class="detail-label">Reference ID</div>
                                <div class="detail-value">{{ file_data.excel_data.get('RefID') }}</div>
                            </div>
                            {% endif %}
                            {% if file_data.excel_data.get('FILE_NO') %}
                            <div class="detail-group">
                                <div class="detail-label">File Number</div>
                                <div class="detail-value">{{ file_data.excel_data.get('FILE_NO') }}</div>
                            </div>
                            {% endif %}
                            {% if file_data.excel_data.get('Category') %}
                            <div class="detail-group">
                                <div class="detail-label">Category</div>
                                <div class="detail-value">{{ file_data.excel_data.get('Category') }}</div>
                            </div>
                            {% endif %}
                            {% if file_data.excel_data.get('hobli_name') %}
                            <div class="detail-group">
                                <div class="detail-label">Hobli</div>
                                <div class="detail-value">{{ file_data.excel_data.get('hobli_name') }}</div>
                            </div>
                            {% endif %}
                            {% if file_data.excel_data.get('village_name') %}
                            <div class="detail-group">
                                <div class="detail-label">Village</div>
                                <div class="detail-value">{{ file_data.excel_data.get('village_name') }}</div>
                            </div>
                            {% endif %}
                            {% if file_data.excel_data.get('survey_no') %}
                            <div class="detail-group">
                                <div class="detail-label">Survey Number</div>
                                <div class="detail-value">{{ file_data.excel_data.get('survey_no') }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <!-- Regular list view for search results -->
        <ul class="files-list">
            {% for file_data in files %}
            <li class="file-item {% if (search_bundle and file_data.bundle_number == search_bundle) or (search_ref_id and file_data.excel_data.get('RefID', '').lower() == search_ref_id.lower()) %}highlighted{% endif %}">
                <div class="file-header">
                    <h3 class="file-title">{{ file_data.file.title }}</h3>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <span class="bundle-badge">Bundle {{ file_data.bundle_number }}</span>
                        <div class="file-actions">
                            <a href="{{ url_for('view_file', file_id=file_data.file.id) }}" class="action-btn primary">
                                <i class="fas fa-eye"></i>View
                            </a>
                            {% if file_data.file.qr_code %}
                            <a href="{{ url_for('get_qrcode', file_id=file_data.file.id) }}" class="action-btn secondary" target="_blank">
                                <i class="fas fa-qrcode"></i>QR Code
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="file-details">
                    {% if file_data.excel_data.get('RefID') %}
                    <div class="detail-group">
                        <div class="detail-label">Reference ID</div>
                        <div class="detail-value">{{ file_data.excel_data.get('RefID') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('FILE_NO') %}
                    <div class="detail-group">
                        <div class="detail-label">File Number</div>
                        <div class="detail-value">{{ file_data.excel_data.get('FILE_NO') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('Category') %}
                    <div class="detail-group">
                        <div class="detail-label">Category</div>
                        <div class="detail-value">{{ file_data.excel_data.get('Category') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('hobli_name') %}
                    <div class="detail-group">
                        <div class="detail-label">Hobli</div>
                        <div class="detail-value">{{ file_data.excel_data.get('hobli_name') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('village_name') %}
                    <div class="detail-group">
                        <div class="detail-label">Village</div>
                        <div class="detail-value">{{ file_data.excel_data.get('village_name') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('survey_no') %}
                    <div class="detail-group">
                        <div class="detail-label">Survey Number</div>
                        <div class="detail-value">{{ file_data.excel_data.get('survey_no') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.location %}
                    <div class="detail-group">
                        <div class="detail-label">Physical Location</div>
                        <div class="detail-value">Rack {{ file_data.location.rack_number }}, Row {{ file_data.location.row_number }}, Position {{ file_data.location.position }}</div>
                    </div>
                    {% endif %}
                </div>
            </li>
            {% endfor %}
        </ul>
        {% endif %}

        <!-- Pagination -->
        {% if files_pagination.pages > 1 %}
        <div class="pagination-section">
            <div class="pagination">
                {% if files_pagination.has_prev %}
                <a href="{{ url_for('compartment_bundles', compartment_number=compartment_number, page=files_pagination.prev_num, bundle=search_bundle, ref_id=search_ref_id) }}"
                   class="page-btn">
                    <i class="fas fa-chevron-left"></i>Previous
                </a>
                {% endif %}

                {% for page_num in range(1, files_pagination.pages + 1) %}
                    {% if page_num != files_pagination.page %}
                    <a href="{{ url_for('compartment_bundles', compartment_number=compartment_number, page=page_num, bundle=search_bundle, ref_id=search_ref_id) }}"
                       class="page-btn">{{ page_num }}</a>
                    {% else %}
                    <span class="page-btn active">{{ page_num }}</span>
                    {% endif %}
                {% endfor %}

                {% if files_pagination.has_next %}
                <a href="{{ url_for('compartment_bundles', compartment_number=compartment_number, page=files_pagination.next_num, bundle=search_bundle, ref_id=search_ref_id) }}"
                   class="page-btn">
                    Next<i class="fas fa-chevron-right ms-1"></i>
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}

        {% else %}
        <div class="no-files">
            <div class="no-files-icon">
                <i class="fas fa-folder-open"></i>
            </div>
            <h4>
                {% if search_ref_id and search_bundle %}
                    No files found with RefID "{{ search_ref_id }}" in Bundle {{ search_bundle }}
                {% elif search_ref_id %}
                    No files found with RefID "{{ search_ref_id }}"
                {% elif search_bundle %}
                    No files found in Bundle {{ search_bundle }}
                {% else %}
                    No files found in Compartment {{ compartment_number }}
                {% endif %}
            </h4>
            <p>
                {% if search_ref_id or search_bundle %}
                    Try searching with different criteria or view all files.
                {% else %}
                    Files may not have been imported yet or may be in a different compartment.
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on search field for mobile
    const refIdInput = document.getElementById('ref_id_search');
    const bundleInput = document.getElementById('bundle_search');
    if (window.innerWidth <= 768) {
        if (refIdInput) refIdInput.focus();
        else if (bundleInput) bundleInput.focus();
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const bundleNumber = document.getElementById('bundle_search').value;

        // Validate bundle number range if provided
        if (bundleNumber) {
            const num = parseInt(bundleNumber);
            const rangeStart = {{ range_start }};
            const rangeEnd = {{ range_end }};

            if (num < rangeStart || num > rangeEnd) {
                e.preventDefault();
                alert(`Bundle number must be between ${rangeStart} and ${rangeEnd} for Compartment {{ compartment_number }}.`);
                return false;
            }
        }
    });

    // Animate file items
    const fileItems = document.querySelectorAll('.file-item');
    fileItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';

        setTimeout(() => {
            item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 50);
    });

    // Highlight search results
    {% if search_bundle or search_ref_id %}
    const highlightedItems = document.querySelectorAll('.file-item.highlighted');
    if (highlightedItems.length > 0) {
        highlightedItems[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    {% endif %}
});
</script>
{% endblock %}
