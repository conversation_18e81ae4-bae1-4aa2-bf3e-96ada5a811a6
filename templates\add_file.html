{% extends "base.html" %}

{% block title %}Add File - T-Office{% endblock %}

{% block styles %}
<style>
.add-file-container {
    max-width: 800px;
    margin: 0 auto;
}

.form-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: 2rem;
}

.form-header {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem;
    text-align: center;
}

.form-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.form-subtitle {
    opacity: 0.9;
    font-size: 1.1rem;
}

.form-body {
    padding: 2rem;
}

.upload-zone {
    border: 3px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    padding: 3rem 2rem;
    text-align: center;
    transition: var(--transition-normal);
    cursor: pointer;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.upload-zone:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.upload-zone.dragover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: 1rem;
}

.upload-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: var(--gray-500);
    font-size: 0.9rem;
}

.file-preview {
    display: none;
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.file-preview.show {
    display: block;
}

.preview-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.preview-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.preview-info h5 {
    margin: 0;
    color: var(--gray-800);
}

.preview-info p {
    margin: 0;
    color: var(--gray-600);
    font-size: 0.9rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.location-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.location-input {
    text-align: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.qr-preview {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 2rem;
    text-align: center;
    margin-bottom: 2rem;
    display: none;
}

.qr-preview.show {
    display: block;
}

.qr-code {
    width: 200px;
    height: 200px;
    margin: 0 auto 1rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-50);
}

.submit-section {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--gray-200);
}

.progress-bar {
    display: none;
    margin-bottom: 1rem;
}

.progress-bar.show {
    display: block;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .location-grid {
        grid-template-columns: 1fr;
    }

    .form-body {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <div class="add-file-container">
        <!-- Form Card -->
        <div class="form-card">
            <div class="form-header">
                <h1 class="form-title">
                    <i class="fas fa-plus-circle me-3"></i>Add New File
                </h1>
                <p class="form-subtitle">Upload a file and assign it a location with QR code generation</p>
            </div>

            <div class="form-body">
                <form method="POST" enctype="multipart/form-data" id="addFileForm">
                    <!-- File Upload Zone -->
                    <div class="upload-zone" id="uploadZone">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">Drop your file here or click to browse</div>
                        <div class="upload-hint">Supports PDF, DOC, XLS, PPT and image files (Max 16MB)<br>
                        <strong>Excel files (.xlsx, .xls) will auto-extract data from columns</strong></div>
                        <input type="file" name="file" id="fileInput" style="display: none;" accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.gif">
                    </div>

                    <!-- File Preview -->
                    <div class="file-preview" id="filePreview">
                        <div class="preview-header">
                            <div class="preview-icon">
                                <i class="fas fa-file" id="previewIcon"></i>
                            </div>
                            <div class="preview-info">
                                <h5 id="previewName">filename.pdf</h5>
                                <p id="previewSize">2.5 MB</p>
                            </div>
                            <button type="button" class="btn btn-outline-danger btn-sm ms-auto" id="removeFile">
                                <i class="fas fa-times"></i> Remove
                            </button>
                        </div>
                    </div>

                    <!-- Excel Row Selection (only shown for Excel files) -->
                    <div class="excel-section" id="excelSection" style="display: none;">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-file-excel me-2"></i>Excel File Detected</h6>
                            <p class="mb-2">This Excel file will automatically extract data from the following columns:</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <small>
                                        <strong>Required columns:</strong><br>
                                        IndexID, RefID, FILE_NO, Category, Year, DisposalCat, Createddate, RowNo, RackNo, RecordRoomSlNo, BundleNo
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small>
                                        <strong>Additional columns:</strong><br>
                                        ClosureDate, ReceiptAtRRDate, DestructionDate, Subject, dist_name_en, taluk_name_en, hobli_name_en, village_name_en, survey_no
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label-modern" for="excel_row_index">Select Row to Process (0 = first data row)</label>
                            <input type="number" class="form-control form-control-modern" id="excel_row_index" name="excel_row_index" value="0" min="0">
                            <small class="form-text text-muted">Row 0 is the first data row (excluding headers)</small>
                        </div>
                    </div>

                    <!-- File Information (hidden for Excel files) -->
                    <div class="form-grid" id="manualFields">
                        <div class="form-group">
                            <label class="form-label-modern" for="title">File Title</label>
                            <input type="text" class="form-control form-control-modern" id="title" name="title">
                            <small class="form-text text-muted" id="titleHint" style="display: none;">Title will be auto-generated from Excel data</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label-modern" for="description">Description</label>
                            <textarea class="form-control form-control-modern" id="description" name="description" rows="3"></textarea>
                            <small class="form-text text-muted" id="descriptionHint" style="display: none;">Description will be auto-generated from Excel data</small>
                        </div>
                    </div>

                    <!-- Location Information -->
                    <h5 class="mb-3"><i class="fas fa-map-marker-alt me-2"></i>File Location</h5>
                    <div class="location-grid" id="locationFields">
                        <div class="form-group">
                            <label class="form-label-modern" for="rack_number">Rack Number</label>
                            <input type="text" class="form-control form-control-modern location-input" id="rack_number" name="rack_number" placeholder="A1">
                            <small class="form-text text-muted" id="rackHint" style="display: none;">Will be auto-filled from RackNo column</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label-modern" for="row_number">Row Number</label>
                            <input type="text" class="form-control form-control-modern location-input" id="row_number" name="row_number" placeholder="3">
                            <small class="form-text text-muted" id="rowHint" style="display: none;">Will be auto-filled from RowNo column</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label-modern" for="position">Position</label>
                            <input type="text" class="form-control form-control-modern location-input" id="position" name="position" placeholder="5">
                            <small class="form-text text-muted" id="positionHint" style="display: none;">Will be auto-filled from RecordRoomSlNo column</small>
                        </div>
                    </div>

                    <!-- QR Code Preview -->
                    <div class="qr-preview" id="qrPreview">
                        <h5><i class="fas fa-qrcode me-2"></i>QR Code Preview</h5>
                        <div class="qr-code" id="qrCode">
                            <i class="fas fa-qrcode fa-3x text-muted"></i>
                        </div>
                        <p class="text-muted">QR code will be generated after file upload</p>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress-bar" id="progressBar">
                        <div class="progress progress-modern">
                            <div class="progress-bar-modern" role="progressbar" style="width: 0%" id="progressBarFill"></div>
                        </div>
                        <p class="text-center mt-2" id="progressText">Uploading...</p>
                    </div>

                    <!-- Submit Section -->
                    <div class="submit-section">
                        <button type="submit" class="btn btn-primary-modern btn-lg" id="submitBtn">
                            <i class="fas fa-save me-2"></i>Add File & Generate QR Code
                        </button>
                        <div class="mt-3">
                            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-modern">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadZone = document.getElementById('uploadZone');
    const fileInput = document.getElementById('fileInput');
    const filePreview = document.getElementById('filePreview');
    const form = document.getElementById('addFileForm');
    const progressBar = document.getElementById('progressBar');

    // File upload handling
    uploadZone.addEventListener('click', () => fileInput.click());
    uploadZone.addEventListener('dragover', handleDragOver);
    uploadZone.addEventListener('dragleave', handleDragLeave);
    uploadZone.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);

    // Remove file
    document.getElementById('removeFile').addEventListener('click', removeFile);

    // Form submission
    form.addEventListener('submit', handleFormSubmit);

    // Auto-generate QR preview
    ['rack_number', 'row_number', 'position'].forEach(id => {
        document.getElementById(id).addEventListener('input', updateQRPreview);
    });

    function handleDragOver(e) {
        e.preventDefault();
        uploadZone.classList.add('dragover');
    }

    function handleDragLeave(e) {
        e.preventDefault();
        uploadZone.classList.remove('dragover');
    }

    function handleDrop(e) {
        e.preventDefault();
        uploadZone.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    }

    function handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            handleFile(file);
        }
    }

    function handleFile(file) {
        // Validate file size (16MB limit)
        if (file.size > 16 * 1024 * 1024) {
            alert('File size must be less than 16MB');
            return;
        }

        // Update preview
        document.getElementById('previewName').textContent = file.name;
        document.getElementById('previewSize').textContent = formatFileSize(file.size);
        document.getElementById('previewIcon').className = `fas fa-${getFileIcon(file.name)}`;

        // Check if it's an Excel file
        const isExcelFile = file.name.toLowerCase().endsWith('.xlsx') || file.name.toLowerCase().endsWith('.xls');

        if (isExcelFile) {
            // Show Excel section and hide manual fields
            document.getElementById('excelSection').style.display = 'block';
            document.getElementById('titleHint').style.display = 'block';
            document.getElementById('descriptionHint').style.display = 'block';
            document.getElementById('rackHint').style.display = 'block';
            document.getElementById('rowHint').style.display = 'block';
            document.getElementById('positionHint').style.display = 'block';

            // Make fields optional for Excel files
            document.getElementById('title').removeAttribute('required');
            document.getElementById('rack_number').removeAttribute('required');
            document.getElementById('row_number').removeAttribute('required');
            document.getElementById('position').removeAttribute('required');

            // Clear manual inputs
            document.getElementById('title').value = '';
            document.getElementById('description').value = '';
            document.getElementById('rack_number').value = '';
            document.getElementById('row_number').value = '';
            document.getElementById('position').value = '';

            // Update submit button text
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-file-excel me-2"></i>Process Excel File & Generate QR Code';
        } else {
            // Hide Excel section and show manual fields
            document.getElementById('excelSection').style.display = 'none';
            document.getElementById('titleHint').style.display = 'none';
            document.getElementById('descriptionHint').style.display = 'none';
            document.getElementById('rackHint').style.display = 'none';
            document.getElementById('rowHint').style.display = 'none';
            document.getElementById('positionHint').style.display = 'none';

            // Make fields required for non-Excel files
            document.getElementById('title').setAttribute('required', 'required');
            document.getElementById('rack_number').setAttribute('required', 'required');
            document.getElementById('row_number').setAttribute('required', 'required');
            document.getElementById('position').setAttribute('required', 'required');

            // Auto-fill title if empty
            const titleInput = document.getElementById('title');
            if (!titleInput.value) {
                titleInput.value = file.name.replace(/\.[^/.]+$/, "");
            }

            // Update submit button text
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Add File & Generate QR Code';
        }

        filePreview.classList.add('show');
        updateQRPreview();
    }

    function removeFile() {
        fileInput.value = '';
        filePreview.classList.remove('show');
        document.getElementById('qrPreview').classList.remove('show');

        // Reset Excel section
        document.getElementById('excelSection').style.display = 'none';
        document.getElementById('titleHint').style.display = 'none';
        document.getElementById('descriptionHint').style.display = 'none';
        document.getElementById('rackHint').style.display = 'none';
        document.getElementById('rowHint').style.display = 'none';
        document.getElementById('positionHint').style.display = 'none';

        // Reset required attributes
        document.getElementById('title').setAttribute('required', 'required');
        document.getElementById('rack_number').setAttribute('required', 'required');
        document.getElementById('row_number').setAttribute('required', 'required');
        document.getElementById('position').setAttribute('required', 'required');

        // Reset submit button text
        document.getElementById('submitBtn').innerHTML = '<i class="fas fa-save me-2"></i>Add File & Generate QR Code';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const icons = {
            'pdf': 'file-pdf',
            'doc': 'file-word',
            'docx': 'file-word',
            'xls': 'file-excel',
            'xlsx': 'file-excel',
            'ppt': 'file-powerpoint',
            'pptx': 'file-powerpoint',
            'jpg': 'file-image',
            'jpeg': 'file-image',
            'png': 'file-image',
            'gif': 'file-image'
        };
        return icons[ext] || 'file';
    }

    function updateQRPreview() {
        const rack = document.getElementById('rack_number').value;
        const row = document.getElementById('row_number').value;
        const position = document.getElementById('position').value;

        if (rack && row && position) {
            document.getElementById('qrPreview').classList.add('show');
        }
    }

    function handleFormSubmit(e) {
        e.preventDefault();

        if (!fileInput.files[0]) {
            alert('Please select a file to upload');
            return;
        }

        // Show progress bar
        progressBar.classList.add('show');
        document.getElementById('submitBtn').disabled = true;

        // Simulate upload progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;

            document.getElementById('progressBarFill').style.width = progress + '%';

            if (progress >= 90) {
                clearInterval(interval);
                // Submit the actual form
                form.submit();
            }
        }, 200);
    }
});
</script>
{% endblock %}
