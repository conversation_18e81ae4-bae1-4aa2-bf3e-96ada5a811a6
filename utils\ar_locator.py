import json
from flask import render_template

def generate_ar_view(file_id, location_data):
    """
    Generate data for AR view to locate a file
    
    Args:
        file_id (int): The ID of the file
        location_data (dict): Location information
        
    Returns:
        dict: AR view configuration
    """
    # Create a 3D representation of the file location
    ar_config = {
        'file_id': file_id,
        'rack': location_data['rack'],
        'row': location_data['row'],
        'position': location_data['position'],
        'markers': [
            {
                'id': 'rack_marker',
                'position': {'x': int(location_data['rack']) * 100, 'y': 0, 'z': 0}
            },
            {
                'id': 'row_marker',
                'position': {'x': int(location_data['rack']) * 100, 'y': int(location_data['row']) * 50, 'z': 0}
            },
            {
                'id': 'file_marker',
                'position': {
                    'x': int(location_data['rack']) * 100, 
                    'y': int(location_data['row']) * 50, 
                    'z': int(location_data['position']) * 25
                }
            }
        ]
    }
    
    return ar_config