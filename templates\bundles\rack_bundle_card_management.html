{% extends "base.html" %}

{% block title %}Rack & Bundle Management - Taluk Office{% endblock %}

{% block content %}
<style>
    /* <PERSON>ck & Bundle Card Management Styles */
    .management-header {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 3rem;
        border-radius: 0 0 30px 30px;
        position: relative;
        overflow: hidden;
    }

    .management-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .management-header .container {
        position: relative;
        z-index: 1;
    }

    .management-header h1 {
        font-size: 3rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .management-header .subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-top: 0.5rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: white;
        border-radius: 25px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(37, 99, 235, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #2563eb, #1d4ed8);
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 900;
        color: #2563eb;
        margin: 0;
        line-height: 1;
    }

    .stat-label {
        color: #6b7280;
        font-size: 1rem;
        font-weight: 600;
        margin-top: 0.5rem;
    }

    .stat-icon {
        font-size: 2.5rem;
        color: #2563eb;
        opacity: 0.8;
        float: right;
        margin-top: -3rem;
    }

    .racks-container {
        margin-bottom: 3rem;
    }

    .rack-card {
        background: white;
        border-radius: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 2rem;
        border: 1px solid rgba(37, 99, 235, 0.1);
    }

    .rack-header {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        padding: 2rem;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .rack-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .rack-header:hover::before {
        transform: translateX(100%);
    }

    .rack-title {
        font-size: 2rem;
        font-weight: 800;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .rack-toggle {
        font-size: 1.5rem;
        transition: transform 0.3s ease;
        opacity: 0.8;
    }

    .rack-card.collapsed .rack-toggle {
        transform: rotate(-90deg);
    }

    .rack-card.collapsed .bundles-container {
        display: none;
    }

    .rack-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1rem;
    }

    .rack-range {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .rack-stats {
        display: flex;
        gap: 2rem;
    }

    .rack-stat {
        text-align: center;
    }

    .rack-stat-number {
        font-size: 1.5rem;
        font-weight: 800;
        margin: 0;
    }

    .rack-stat-label {
        font-size: 0.85rem;
        opacity: 0.8;
        margin: 0;
    }

    .bundles-container {
        padding: 2rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .bundles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
    }

    .bundle-card {
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 15px;
        padding: 1rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .bundle-card.has-files {
        border-color: #10b981;
        background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    }

    .bundle-card.has-files::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #10b981, #059669);
    }

    .bundle-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .bundle-card.has-files:hover {
        border-color: #059669;
        background: linear-gradient(135deg, #d1fae5 0%, #bbf7d0 100%);
    }

    .bundle-number {
        font-size: 1.2rem;
        font-weight: 800;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .bundle-card.has-files .bundle-number {
        color: #059669;
    }

    .bundle-file-count {
        font-size: 0.8rem;
        color: #6b7280;
        font-weight: 600;
    }

    .bundle-card.has-files .bundle-file-count {
        color: #047857;
        background: rgba(16, 185, 129, 0.1);
        padding: 0.25rem 0.5rem;
        border-radius: 10px;
        margin-top: 0.5rem;
        display: inline-block;
    }

    .empty-state {
        text-align: center;
        padding: 4rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 5rem;
        margin-bottom: 1.5rem;
        opacity: 0.3;
        color: #2563eb;
    }

    .search-filters {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(37, 99, 235, 0.1);
    }

    .search-filters .form-control {
        border-radius: 15px;
        border: 2px solid #e5e7eb;
        padding: 1rem 1.25rem;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .search-filters .form-control:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
        transform: translateY(-2px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .bundles-grid {
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        }

        .management-header h1 {
            font-size: 2.2rem;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .rack-stats {
            gap: 1rem;
        }
    }
</style>

<!-- Management Header -->
<div class="management-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-boxes me-3"></i>Rack & Bundle Management</h1>
            <p class="subtitle">Card-Style Interface | Rack 1 (1-40), Rack 2 (41-80), Rack 3 (81-120)...</p>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ total_racks }}</div>
            <div class="stat-label">Active Racks</div>
            <div class="stat-icon"><i class="fas fa-warehouse"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_bundles }}</div>
            <div class="stat-label">Bundles with Files</div>
            <div class="stat-icon"><i class="fas fa-folder"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_files }}</div>
            <div class="stat-label">Total Files</div>
            <div class="stat-icon"><i class="fas fa-file-alt"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_villages }}</div>
            <div class="stat-label">Villages</div>
            <div class="stat-icon"><i class="fas fa-map-marker-alt"></i></div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <div class="row">
            <div class="col-md-6">
                <input type="text" class="form-control" id="rackSearch" placeholder="🏢 Search racks..." onkeyup="filterRacks()">
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control" id="bundleSearch" placeholder="📁 Search bundles..." onkeyup="filterBundles()">
            </div>
        </div>
    </div>

    <!-- Racks Container -->
    <div class="racks-container">
        {% if rack_structure %}
            {% for rack_num, rack_data in rack_structure.items() %}
                {% if rack_data.total_files > 0 %}
                <div class="rack-card" data-rack="{{ rack_num }}">
                    <div class="rack-header" onclick="toggleRack('{{ rack_num }}')">
                        <div>
                            <div class="rack-title">
                                <i class="fas fa-chevron-down rack-toggle"></i>
                                <i class="fas fa-warehouse me-2"></i>
                                Rack {{ rack_num }}
                            </div>
                            
                            <div class="rack-info">
                                <div class="rack-range">
                                    Bundles {{ rack_data.start_bundle }} - {{ rack_data.end_bundle }}
                                </div>
                                
                                <div class="rack-stats">
                                    <div class="rack-stat">
                                        <div class="rack-stat-number">{{ rack_data.total_bundles_with_files }}</div>
                                        <div class="rack-stat-label">Active Bundles</div>
                                    </div>
                                    <div class="rack-stat">
                                        <div class="rack-stat-number">{{ rack_data.total_files }}</div>
                                        <div class="rack-stat-label">Total Files</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bundles-container">
                        <div class="bundles-grid">
                            {% for bundle_no, bundle_data in rack_data.bundles.items() %}
                            <div class="bundle-card {% if bundle_data.has_files %}has-files{% endif %}" 
                                 data-bundle="{{ bundle_no }}" 
                                 {% if bundle_data.has_files %}onclick="viewBundle({{ bundle_no }})"{% endif %}>
                                <div class="bundle-number">{{ bundle_no }}</div>
                                {% if bundle_data.has_files %}
                                    <div class="bundle-file-count">{{ bundle_data.total_files }} files</div>
                                {% else %}
                                    <div class="bundle-file-count">Empty</div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endif %}
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <i class="fas fa-boxes"></i>
                <h3>No Bundle Data Found</h3>
                <p>Upload Excel files with bundle information to see rack and bundle organization.</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
    // Rack toggle functionality
    function toggleRack(rackNum) {
        const rackCard = document.querySelector(`[data-rack="${rackNum}"]`);
        rackCard.classList.toggle('collapsed');
        
        // Save collapsed state to localStorage
        const collapsedRacks = JSON.parse(localStorage.getItem('collapsedRacks') || '[]');
        if (rackCard.classList.contains('collapsed')) {
            if (!collapsedRacks.includes(rackNum)) {
                collapsedRacks.push(rackNum);
            }
        } else {
            const index = collapsedRacks.indexOf(rackNum);
            if (index > -1) {
                collapsedRacks.splice(index, 1);
            }
        }
        localStorage.setItem('collapsedRacks', JSON.stringify(collapsedRacks));
    }
    
    // Bundle view functionality
    function viewBundle(bundleNo) {
        window.location.href = `/bundles/bundle/${bundleNo}`;
    }
    
    // Filter racks
    function filterRacks() {
        const searchTerm = document.getElementById('rackSearch').value.toLowerCase();
        const rackCards = document.querySelectorAll('.rack-card');
        
        rackCards.forEach(card => {
            const rackNum = card.dataset.rack.toLowerCase();
            if (rackNum.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    // Filter bundles
    function filterBundles() {
        const searchTerm = document.getElementById('bundleSearch').value.toLowerCase();
        const bundleCards = document.querySelectorAll('.bundle-card');
        
        bundleCards.forEach(card => {
            const bundleNo = card.dataset.bundle.toLowerCase();
            if (bundleNo.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    // Restore collapsed state on page load
    document.addEventListener('DOMContentLoaded', function() {
        const collapsedRacks = JSON.parse(localStorage.getItem('collapsedRacks') || '[]');
        collapsedRacks.forEach(rackNum => {
            const rackCard = document.querySelector(`[data-rack="${rackNum}"]`);
            if (rackCard) {
                rackCard.classList.add('collapsed');
            }
        });
    });
</script>
{% endblock %}
