{% extends "base.html" %}

{% block title %}Taluk Office - Digital File Management System{% endblock %}

{% block styles %}
<style>
.hero-section {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
    min-height: 80vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1.5rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.cta-section {
    background: var(--gray-50);
    padding: 5rem 0;
}

.demo-video {
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title">Digital Transformation for Government Offices</h1>
                    <p class="hero-subtitle">
                        Modern file management system with QR code tracking, digital archiving,
                        and advanced search capabilities. Streamline your Taluk Office operations with cutting-edge technology.
                    </p>
                    <div class="d-flex gap-3 flex-wrap">
                        {% if current_user.is_authenticated %}
                        <a href="{{ url_for('dashboard') }}" class="btn btn-light btn-lg btn-modern">
                            <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                        </a>
                        {% else %}
                        <a href="{{ url_for('login') }}" class="btn btn-light btn-lg btn-modern">
                            <i class="fas fa-sign-in-alt me-2"></i>Get Started
                        </a>
                        {% endif %}
                        <button class="btn btn-outline-light btn-lg btn-modern" onclick="scrollToFeatures()">
                            <i class="fas fa-play me-2"></i>Watch Demo
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="demo-video">
                    <img src="https://via.placeholder.com/600x400/667eea/ffffff?text=T-Office+Demo"
                         alt="T-Office Demo" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-4 fw-bold text-gradient mb-3">Government-Grade Features</h2>
            <p class="lead text-muted">Designed specifically for government office requirements</p>
        </div>

        <div class="row g-4">
            <div class="col-md-6 col-lg-4">
                <div class="feature-card hover-lift">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Digital File Tracking</h4>
                    <p class="text-muted">Generate unique QR codes for government documents. Track file movement and maintain complete audit trails for transparency.</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-4">
                <div class="feature-card hover-lift">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Smart Search System</h4>
                    <p class="text-muted">Advanced search capabilities to quickly locate documents by reference number, date, or content keywords.</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-4">
                <div class="feature-card hover-lift">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Secure Access Control</h4>
                    <p class="text-muted">Role-based access control ensuring only authorized personnel can access sensitive government documents.</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-4">
                <div class="feature-card hover-lift">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Digital Archive</h4>
                    <p class="text-muted">Comprehensive digital archiving system with backup and recovery capabilities for long-term document preservation.</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-4">
                <div class="feature-card hover-lift">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Workflow Management</h4>
                    <p class="text-muted">Streamlined workflow for document approval, routing, and status tracking across departments.</p>
                </div>
            </div>

            <div class="col-md-6 col-lg-4">
                <div class="feature-card hover-lift">
                    <div class="feature-icon mx-auto">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Compliance & Reporting</h4>
                    <p class="text-muted">Generate compliance reports, audit trails, and statistical analysis for government transparency requirements.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container text-center">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <h2 class="display-5 fw-bold mb-4">Ready to Transform Your File Management?</h2>
                <p class="lead text-muted mb-4">
                    Join thousands of organizations already using T-Office to streamline their file management processes.
                </p>
                {% if not current_user.is_authenticated %}
                <a href="{{ url_for('login') }}" class="btn btn-primary-modern btn-lg">
                    <i class="fas fa-rocket me-2"></i>Start Free Trial
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block scripts %}
<script>
function scrollToFeatures() {
    document.getElementById('features').scrollIntoView({
        behavior: 'smooth'
    });
}

// Add some interactive animations
document.addEventListener('DOMContentLoaded', function() {
    // Animate feature cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    document.querySelectorAll('.feature-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>
{% endblock %}
