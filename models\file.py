from extensions import db
from datetime import datetime

class CompartmentQR(db.Model):
    __tablename__ = 'compartment_qrs'

    id = db.Column(db.Integer, primary_key=True)
    compartment_number = db.Column(db.Integer, nullable=False, unique=True)
    bundle_range_start = db.Column(db.Integer, nullable=False)
    bundle_range_end = db.Column(db.Integer, nullable=False)
    qr_image_path = db.Column(db.String(255), nullable=False)
    qr_data = db.Column(db.Text, nullable=False)  # JSON string of the QR code data
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.<PERSON>, default=True)

    def __repr__(self):
        return f'<CompartmentQR Compartment {self.compartment_number}: Bundles {self.bundle_range_start}-{self.bundle_range_end}>'

    def get_bundle_list(self):
        """Return the list of bundle numbers for this compartment"""
        return list(range(self.bundle_range_start, self.bundle_range_end + 1))

class File(db.Model):
    __tablename__ = 'files'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.Text, nullable=False)  # Changed to Text for unlimited length
    description = db.Column(db.Text)
    filename = db.Column(db.String(255), nullable=True)  # Stored filename (nullable for Excel imports)
    original_filename = db.Column(db.String(255))  # Original filename
    qr_code = db.Column(db.String(255))  # QR code image filename
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Excel import data preservation fields
    excel_row_data = db.Column(db.Text)  # Complete Excel row as JSON for 100% data preservation
    import_warnings = db.Column(db.Text)  # Non-blocking validation warnings
    data_quality_score = db.Column(db.Integer, default=100)  # Quality indicator (0-100)
    import_batch_id = db.Column(db.String(50))  # Track which batch this record came from
    original_row_number = db.Column(db.Integer)  # Original Excel row number

    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    location_id = db.Column(db.Integer, db.ForeignKey('locations.id'))

    # Relationships
    access_logs = db.relationship('AccessLog', backref='file', lazy='dynamic')

    def __repr__(self):
        return f'<File {self.title}>'