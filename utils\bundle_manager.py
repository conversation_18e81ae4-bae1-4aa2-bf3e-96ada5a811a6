import json
import qrcode
import os
from datetime import datetime
from flask import current_app
from models.bundle import Bundle, BundleAssignment, BundleStatistics
from models.file import File, CompartmentQR
from extensions import db


class BundleManager:
    """Comprehensive bundle management system"""

    @staticmethod
    def initialize_bundles():
        """Initialize all 800 bundles if they don't exist"""
        created_bundles = []

        for bundle_num in range(1, 801):  # Extended to 1-800
            existing_bundle = Bundle.query.filter_by(bundle_number=bundle_num).first()

            if not existing_bundle:
                # Determine compartment based on bundle number
                compartment = BundleManager.get_compartment_for_bundle(bundle_num)

                bundle = Bundle(
                    bundle_number=bundle_num,
                    bundle_name=f"Bundle {bundle_num}",
                    description=f"Government document bundle {bundle_num}",
                    max_capacity=100,  # Default capacity
                    compartment_id=compartment.id if compartment else None
                )

                db.session.add(bundle)
                created_bundles.append(bundle_num)

        if created_bundles:
            db.session.commit()

            # Fix compartment assignments to ensure correct distribution
            BundleManager.fix_compartment_assignments()

            # Update file counts for all bundles
            BundleManager.update_all_bundle_counts()

            # Generate QR codes for new bundles
            for bundle_num in created_bundles:
                bundle = Bundle.query.filter_by(bundle_number=bundle_num).first()
                BundleManager.generate_bundle_qr_code(bundle)

        return created_bundles

    @staticmethod
    def get_compartment_for_bundle(bundle_number):
        """Determine which compartment a bundle belongs to

        Bundles 1-400 are assigned to Compartment 1.
        Bundles 401-800 are assigned to Compartment 2.
        """
        if 1 <= bundle_number <= 400:
            # Find Compartment 1 (bundles 1-400)
            compartment_1 = CompartmentQR.query.filter_by(compartment_number=1).first()
            if compartment_1:
                return compartment_1
        elif 401 <= bundle_number <= 800:
            # Find Compartment 2 (bundles 401-800)
            compartment_2 = CompartmentQR.query.filter_by(compartment_number=2).first()
            if compartment_2:
                return compartment_2

        # Invalid bundle number or compartment not found
        return None

    @staticmethod
    def update_all_bundle_counts():
        """Update file counts for all bundles"""
        bundles = Bundle.query.all()

        for bundle in bundles:
            bundle.update_file_count()

        return len(bundles)

    @staticmethod
    def get_bundle_files(bundle_number):
        """Get all files for a specific bundle number"""
        files = []

        # Query all files with excel_row_data
        all_files = File.query.filter(File.excel_row_data.isnot(None)).all()

        for file in all_files:
            try:
                excel_data = json.loads(file.excel_row_data)
                bundle_no = excel_data.get('BundleNo', '')

                if bundle_no:
                    # Handle different formats
                    try:
                        file_bundle_no = int(float(str(bundle_no)))
                        if file_bundle_no == bundle_number:
                            files.append(file)
                    except (ValueError, TypeError):
                        if str(bundle_no).strip() == str(bundle_number):
                            files.append(file)
            except (json.JSONDecodeError, AttributeError):
                continue

        return files

    @staticmethod
    def search_files_in_bundle(bundle_number, search_criteria):
        """Search for files within a specific bundle"""
        bundle_files = BundleManager.get_bundle_files(bundle_number)

        if not search_criteria:
            return bundle_files

        filtered_files = []

        for file in bundle_files:
            match = False

            # Search in title
            if search_criteria.lower() in file.title.lower():
                match = True

            # Search in excel_row_data
            if not match and file.excel_row_data:
                try:
                    excel_data = json.loads(file.excel_row_data)
                    for key, value in excel_data.items():
                        if value and search_criteria.lower() in str(value).lower():
                            match = True
                            break
                except json.JSONDecodeError:
                    pass

            if match:
                filtered_files.append(file)

        return filtered_files

    @staticmethod
    def reassign_file_to_bundle(file_id, new_bundle_number, user_id, reason="Manual reassignment"):
        """Reassign a file to a different bundle"""
        file = File.query.get(file_id)
        if not file or not file.excel_row_data:
            return False, "File not found or no Excel data"

        try:
            excel_data = json.loads(file.excel_row_data)
            old_bundle_no = excel_data.get('BundleNo', '')

            # Update the bundle number in excel_row_data
            excel_data['BundleNo'] = str(new_bundle_number)
            file.excel_row_data = json.dumps(excel_data)

            # Create assignment record
            old_bundle = Bundle.query.filter_by(bundle_number=int(float(str(old_bundle_no)))).first() if old_bundle_no else None
            new_bundle = Bundle.query.filter_by(bundle_number=new_bundle_number).first()

            assignment = BundleAssignment(
                file_id=file_id,
                bundle_id=new_bundle.id if new_bundle else None,
                assigned_by=user_id,
                assignment_type='manual',
                assignment_reason=reason,
                previous_bundle_id=old_bundle.id if old_bundle else None
            )

            db.session.add(assignment)
            db.session.commit()

            # Update bundle counts
            if old_bundle:
                old_bundle.update_file_count()
            if new_bundle:
                new_bundle.update_file_count()

            return True, f"File reassigned from bundle {old_bundle_no} to bundle {new_bundle_number}"

        except Exception as e:
            db.session.rollback()
            return False, f"Error reassigning file: {str(e)}"

    @staticmethod
    def generate_bundle_qr_code(bundle, force_regenerate=False):
        """Generate STATIC QR code for a bundle (only if it doesn't exist)

        Args:
            bundle: Bundle object to generate QR code for
            force_regenerate: If True, regenerate even if QR code exists (use with caution)

        Returns:
            str: QR code filename if successful, None otherwise

        IMPORTANT: This method ensures QR codes are IMMUTABLE and generated only once.
        QR codes are NOT regenerated when bundle contents change to maintain
        physical QR code validity.
        """
        if not bundle:
            return None

        # Check if QR code already exists (IMMUTABILITY CHECK)
        qr_filename = f"bundle_{bundle.bundle_number}_qr.png"
        qr_folder = current_app.config.get('QR_CODE_FOLDER', 'static/qrcodes')
        qr_path = os.path.join(qr_folder, qr_filename)

        if os.path.exists(qr_path) and bundle.qr_code_path and not force_regenerate:
            # QR code already exists - DO NOT regenerate to maintain immutability
            current_app.logger.info(f"Bundle {bundle.bundle_number} QR code already exists - preserving immutability")
            return qr_filename

        # Generate STATIC QR code data (immutable content only)
        qr_data = bundle.generate_qr_code()

        try:
            # Create QR code with STATIC content
            qr = qrcode.QRCode(
                version=2,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=10,
                border=4,
            )

            qr.add_data(json.dumps(qr_data))
            qr.make(fit=True)

            # Create QR code image
            qr_img = qr.make_image(fill_color="black", back_color="white")

            # Save QR code image (ONCE ONLY)
            qr_img.save(qr_path)

            # Update bundle with QR code path
            bundle.qr_code_path = qr_filename
            db.session.commit()

            current_app.logger.info(f"Generated STATIC QR code for bundle {bundle.bundle_number}")
            return qr_filename

        except Exception as e:
            current_app.logger.error(f"Error generating QR code for bundle {bundle.bundle_number}: {str(e)}")
            return None

    @staticmethod
    def get_bundle_statistics():
        """Get comprehensive bundle statistics"""
        bundles = Bundle.query.order_by(Bundle.bundle_number).all()

        stats = {
            'total_bundles': len(bundles),
            'active_bundles': 0,
            'empty_bundles': 0,
            'full_bundles': 0,
            'total_files': 0,
            'avg_capacity_usage': 0,
            'bundles_by_status': {
                'empty': [],
                'active': [],
                'nearly_full': [],
                'full': []
            }
        }

        capacity_percentages = []

        for bundle in bundles:
            bundle.update_file_count()  # Ensure counts are current

            status = bundle.get_status()
            stats['bundles_by_status'][status].append(bundle.bundle_number)

            if status == 'empty':
                stats['empty_bundles'] += 1
            elif status == 'full':
                stats['full_bundles'] += 1
            else:
                stats['active_bundles'] += 1

            stats['total_files'] += bundle.current_count
            capacity_percentages.append(bundle.get_capacity_percentage())

        if capacity_percentages:
            stats['avg_capacity_usage'] = sum(capacity_percentages) / len(capacity_percentages)

        return stats

    @staticmethod
    def get_bundle_distribution_by_compartment():
        """Get bundle distribution across compartments

        Only Compartment 1 has bundle assignments (bundles 1-40).
        Other compartments are shown as available but unassigned.
        """
        compartments = CompartmentQR.query.all()
        distribution = {}

        for compartment in compartments:
            bundles_in_compartment = Bundle.query.filter_by(compartment_id=compartment.id).all()

            # Special handling for compartment display
            if compartment.compartment_number == 1:
                # Compartment 1 has actual bundle assignments
                bundle_range_display = f"Bundles 1-40 (Active)"
            elif compartment.compartment_number == 2:
                # Compartment 2 is available but unassigned
                bundle_range_display = f"Available (No bundles assigned)"
            else:
                # Other compartments
                bundle_range_display = f"Range {compartment.bundle_range_start}-{compartment.bundle_range_end} (Available)"

            distribution[compartment.compartment_number] = {
                'compartment_number': compartment.compartment_number,
                'bundle_range': bundle_range_display,
                'original_range': f"{compartment.bundle_range_start}-{compartment.bundle_range_end}",
                'total_bundles': len(bundles_in_compartment),
                'total_files': sum(bundle.current_count for bundle in bundles_in_compartment),
                'is_active': len(bundles_in_compartment) > 0,
                'bundles': [
                    {
                        'bundle_number': bundle.bundle_number,
                        'file_count': bundle.current_count,
                        'capacity': bundle.max_capacity,
                        'status': bundle.get_status()
                    }
                    for bundle in bundles_in_compartment
                ]
            }

        return distribution

    @staticmethod
    def fix_compartment_assignments():
        """Fix incorrect compartment assignments for bundles

        Ensures only bundles 1-40 are assigned to Compartment 1,
        and removes any incorrect assignments to other compartments.
        """
        fixes_applied = []

        try:
            # Get Compartment 1
            compartment_1 = CompartmentQR.query.filter_by(compartment_number=1).first()

            if not compartment_1:
                return ["Error: Compartment 1 not found"]

            # Get all bundles
            all_bundles = Bundle.query.all()

            for bundle in all_bundles:
                if 1 <= bundle.bundle_number <= 40:
                    # These bundles should be assigned to Compartment 1
                    if bundle.compartment_id != compartment_1.id:
                        old_compartment = bundle.compartment_id
                        bundle.compartment_id = compartment_1.id
                        fixes_applied.append(
                            f"Bundle {bundle.bundle_number}: Moved from compartment {old_compartment} to Compartment 1"
                        )
                else:
                    # Bundles outside 1-40 range should not be assigned to any compartment
                    if bundle.compartment_id is not None:
                        old_compartment = bundle.compartment_id
                        bundle.compartment_id = None
                        fixes_applied.append(
                            f"Bundle {bundle.bundle_number}: Removed from compartment {old_compartment} (out of range)"
                        )

            # Remove any bundles that might be incorrectly assigned to Compartment 2 or others
            compartment_2 = CompartmentQR.query.filter_by(compartment_number=2).first()
            if compartment_2:
                bundles_in_comp_2 = Bundle.query.filter_by(compartment_id=compartment_2.id).all()
                for bundle in bundles_in_comp_2:
                    bundle.compartment_id = None
                    fixes_applied.append(
                        f"Bundle {bundle.bundle_number}: Removed from Compartment 2 (reserved for future use)"
                    )

            db.session.commit()

            if not fixes_applied:
                fixes_applied.append("No compartment assignment fixes needed - all bundles correctly assigned")

        except Exception as e:
            db.session.rollback()
            fixes_applied.append(f"Error fixing compartment assignments: {str(e)}")

        return fixes_applied

    @staticmethod
    def assign_file_to_compartment_by_bundle(file_id, bundle_number, warnings_list=None):
        """Enhanced automatic assignment of file to compartment based on bundle number during bulk upload"""
        if warnings_list is None:
            warnings_list = []

        try:
            # Enhanced validation with detailed feedback
            if not isinstance(bundle_number, int):
                try:
                    bundle_number = int(float(str(bundle_number)))
                except (ValueError, TypeError):
                    warning_msg = f"Bundle number '{bundle_number}' is not a valid integer"
                    warnings_list.append(warning_msg)
                    return None, warning_msg

            # Validate bundle number range with specific compartment feedback
            if not (1 <= bundle_number <= 800):
                if bundle_number < 1:
                    warning_msg = f"Bundle number {bundle_number} is too low (minimum: 1)"
                elif bundle_number > 800:
                    warning_msg = f"Bundle number {bundle_number} is too high (maximum: 800)"
                else:
                    warning_msg = f"Invalid bundle number {bundle_number}, must be 1-800"
                warnings_list.append(warning_msg)
                return None, warning_msg

            # Determine expected compartment for validation
            expected_compartment = 1 if bundle_number <= 400 else 2

            # Get or create the bundle with enhanced error handling
            bundle = Bundle.query.filter_by(bundle_number=bundle_number).first()
            if not bundle:
                # Create bundle if it doesn't exist
                compartment = BundleManager.get_compartment_for_bundle(bundle_number)
                if not compartment:
                    warning_msg = f"No compartment found for bundle {bundle_number}"
                    warnings_list.append(warning_msg)
                    return None, warning_msg

                bundle = Bundle(
                    bundle_number=bundle_number,
                    bundle_name=f"Bundle {bundle_number}",
                    description=f"Auto-created bundle {bundle_number} during bulk upload (Compartment {expected_compartment})",
                    max_capacity=100,
                    compartment_id=compartment.id
                )
                db.session.add(bundle)
                db.session.flush()  # Get bundle ID

                # Generate QR code for new bundle
                try:
                    BundleManager.generate_bundle_qr_code(bundle)
                    warnings_list.append(f"✅ Created Bundle {bundle_number} in Compartment {expected_compartment}")
                except Exception as qr_error:
                    warnings_list.append(f"⚠️ Bundle {bundle_number} created but QR generation failed: {str(qr_error)}")
            else:
                # Validate existing bundle is in correct compartment
                if bundle.compartment and bundle.compartment.compartment_number != expected_compartment:
                    warning_msg = f"⚠️ Bundle {bundle_number} exists in Compartment {bundle.compartment.compartment_number}, expected Compartment {expected_compartment}"
                    warnings_list.append(warning_msg)

            # Update bundle file count
            bundle.update_file_count()

            # Create comprehensive compartment assignment info
            compartment_info = {
                'bundle_number': bundle_number,
                'compartment_id': bundle.compartment_id,
                'compartment_number': bundle.compartment.compartment_number if bundle.compartment else None,
                'bundle_id': bundle.id,
                'expected_compartment': expected_compartment,
                'assignment_status': 'success',
                'bundle_created': bundle.id is not None
            }

            return compartment_info, None

        except Exception as e:
            error_msg = f"Critical error assigning file to compartment: {str(e)}"
            warnings_list.append(error_msg)
            return None, error_msg

    @staticmethod
    def validate_bundle_data_integrity():
        """Validate bundle data integrity and fix inconsistencies"""
        issues = []
        fixes = []

        # Check for files with invalid bundle numbers
        all_files = File.query.filter(File.excel_row_data.isnot(None)).all()

        for file in all_files:
            try:
                excel_data = json.loads(file.excel_row_data)
                bundle_no = excel_data.get('BundleNo', '')

                if bundle_no:
                    try:
                        bundle_num = int(float(str(bundle_no)))
                        if bundle_num < 1 or bundle_num > 800:  # Updated range to 1-800
                            issues.append(f"File {file.id} has invalid bundle number: {bundle_num}")
                    except (ValueError, TypeError):
                        issues.append(f"File {file.id} has non-numeric bundle number: {bundle_no}")

            except json.JSONDecodeError:
                issues.append(f"File {file.id} has invalid JSON in excel_row_data")

        # Check for bundles without corresponding Bundle records
        for bundle_num in range(1, 41):
            bundle = Bundle.query.filter_by(bundle_number=bundle_num).first()
            if not bundle:
                issues.append(f"Bundle {bundle_num} record missing")
                fixes.append(f"Created Bundle {bundle_num} record")

        return {
            'issues': issues,
            'fixes': fixes,
            'total_issues': len(issues),
            'integrity_score': max(0, 100 - (len(issues) * 2))  # Deduct 2 points per issue
        }
