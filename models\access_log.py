from extensions import db
from datetime import datetime
import json

class AccessLog(db.Model):
    __tablename__ = 'access_logs'

    id = db.Column(db.Integer, primary_key=True)
    action = db.Column(db.String(50), nullable=False)  # created, viewed, downloaded, scanned, bulk_data_accessed, bulk_data_unlocked, etc.
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    # Enhanced security fields for bulk data access
    search_criteria = db.Column(db.Text)  # JSON of search criteria used
    access_session_id = db.Column(db.String(100))  # Session ID for bulk data access
    ip_address = db.Column(db.String(45))  # User's IP address
    user_agent = db.Column(db.Text)  # User's browser/client info
    is_bulk_data = db.Column(db.Bo<PERSON>, default=False)  # Flag for bulk uploaded data access
    access_granted = db.Column(db.<PERSON>, default=True)  # Whether access was granted or denied
    denial_reason = db.Column(db.String(255))  # Reason for access denial

    # Foreign keys
    file_id = db.Column(db.Integer, db.ForeignKey('files.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # Nullable for anonymous access

    def __repr__(self):
        return f'<AccessLog {self.action} on File {self.file_id} by User {self.user_id}>'

    def set_search_criteria(self, criteria_dict):
        """Store search criteria as JSON"""
        if criteria_dict:
            self.search_criteria = json.dumps(criteria_dict)

    def get_search_criteria(self):
        """Retrieve search criteria as dictionary"""
        if self.search_criteria:
            try:
                return json.loads(self.search_criteria)
            except json.JSONDecodeError:
                return {}
        return {}