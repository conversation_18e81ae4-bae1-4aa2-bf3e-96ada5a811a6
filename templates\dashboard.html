{% extends "base.html" %}

{% block title %}Dashboard - Taluk Office{% endblock %}

{% block styles %}
<style>
/* Import official color variables */
:root {
    --primary-color: #003366;
    --primary-dark: #002244;
    --primary-light: #004488;
    --secondary-color: #8B0000;
    --success-color: #006600;
    --warning-color: #CC6600;
    --danger-color: #CC0000;
    --info-color: #004488;
    --gold-color: #FFD700;
    --white: #ffffff;
    --gray-50: #f8f9fa;
    --gray-100: #f1f3f4;
    --gray-200: #e8eaed;
    --gray-300: #dadce0;
    --gray-400: #9aa0a6;
    --gray-500: #5f6368;
    --gray-600: #3c4043;
    --gray-700: #202124;
    --gray-800: #1a1a1a;
    --gray-900: #000000;
}

/* Dashboard Navigation */
.dashboard-nav {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-bottom: 3px solid var(--gold-color);
    box-shadow: 0 2px 10px rgba(0,0,0,0.15);
    margin-bottom: 0;
}

.dashboard-nav-pills {
    border: none;
    padding: 1rem 0;
}

.dashboard-nav-pills .nav-link {
    color: white;
    font-weight: 600;
    border-radius: 8px;
    margin-right: 1rem;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.dashboard-nav-pills .nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.dashboard-nav-pills .nav-link.active {
    background: white;
    color: var(--primary-color);
    border-color: white;
}

.dashboard-nav .dropdown-menu {
    border: none;
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    border-radius: 8px;
    margin-top: 0.5rem;
}

.dashboard-nav .dropdown-item {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dashboard-nav .dropdown-item:hover {
    background: var(--primary-color);
    color: white;
}

/* Dashboard Header */
.dashboard-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.welcome-text {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    color: var(--gray-600);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.user-role-badge {
    margin-top: 0.5rem;
}

.user-role-badge .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background: var(--primary-color);
}

.dashboard-info {
    text-align: right;
}

.info-item {
    display: block;
    color: var(--gray-600);
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.info-item i {
    color: var(--primary-color);
}

/* Breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.breadcrumb-item a:hover {
    color: var(--primary-dark);
}

.breadcrumb-item.active {
    color: #6c757d;
    font-weight: 600;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: bold;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-normal);
    text-align: center;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stats-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white);
    font-size: 2rem;
}

.stats-icon.success {
    background: var(--gradient-success);
}

.stats-icon.warning {
    background: linear-gradient(135deg, var(--warning-color), #fd7e14);
}

.stats-icon.info {
    background: linear-gradient(135deg, var(--info-color), var(--primary-light));
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.stats-label {
    color: var(--gray-600);
    font-weight: 600;
    font-size: 1rem;
}

/* Recent Files Section */
.recent-files {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.section-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.files-list {
    padding: 0;
    margin: 0;
    list-style: none;
}

/* File Items */
.file-item {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.file-item:hover {
    background: var(--gray-50);
}

.file-item:last-child {
    border-bottom: none;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
}

.file-meta {
    color: var(--gray-600);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    text-decoration: none;
    font-size: 0.9rem;
}

.action-btn.view {
    background: rgba(30, 64, 175, 0.1);
    color: var(--primary-color);
}

.action-btn.view:hover {
    background: var(--primary-color);
    color: white;
}

.action-btn.download {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.action-btn.download:hover {
    background: var(--success-color);
    color: white;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--gray-500);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.btn-primary-modern {
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--radius-lg);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition-normal);
    color: white;
}

.btn-primary-modern:hover {
    background: var(--gradient-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

/* Activity Feed */
.activity-feed {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-top: 2rem;
}

/* Activity Items */
.activity-item {
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-icon.created {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.activity-icon.viewed {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.activity-icon.scanned {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.activity-content {
    flex: 1;
}

.activity-text {
    margin: 0;
    color: var(--gray-800);
    font-weight: 500;
}

.activity-time {
    color: var(--gray-500);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

@media (max-width: 992px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .dashboard-nav-pills {
        flex-direction: column;
        align-items: stretch;
    }

    .dashboard-nav-pills .nav-link {
        margin-right: 0;
        margin-bottom: 0.5rem;
        text-align: center;
    }

    .welcome-text {
        font-size: 1.8rem;
    }

    .dashboard-info {
        text-align: left;
        margin-top: 1rem;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 1.5rem;
    }

    .welcome-text {
        font-size: 1.6rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stats-card {
        padding: 1.5rem;
    }

    .stats-number {
        font-size: 2rem;
    }

    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .file-actions {
        align-self: stretch;
        justify-content: center;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Dashboard Navigation -->
<nav class="dashboard-nav">
    <div class="container-fluid px-4">
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-pills dashboard-nav-pills">
                    <!-- File Operations -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button">
                            <i class="fas fa-file-alt me-2"></i>File Operations
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('add_file') }}">
                                <i class="fas fa-file-plus me-2"></i>Add Document
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('upload_excel') }}">
                                <i class="fas fa-file-excel me-2"></i>Upload Excel
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('compartment_search') }}">
                                <i class="fas fa-search me-2"></i>Search Files
                            </a></li>
                        </ul>
                    </li>

                    <!-- Bundle Management -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button">
                            <i class="fas fa-layer-group me-2"></i>Bundle Management
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('bundle_list') }}">
                                <i class="fas fa-layer-group me-2"></i>All Bundles (1-800)
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('compartment_bundles', compartment_number=1) }}">
                                <i class="fas fa-archive me-2"></i>Compartment 1 (Bundles 1-400)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('compartment_bundles', compartment_number=2) }}">
                                <i class="fas fa-archive me-2"></i>Compartment 2 (Bundles 401-800)
                            </a></li>
                        </ul>
                    </li>

                    <!-- QR Operations -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button">
                            <i class="fas fa-qrcode me-2"></i>QR Operations
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('scan_qrcode') }}">
                                <i class="fas fa-qrcode me-2"></i>QR Scanner
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('compartment_qr_management') }}">
                                <i class="fas fa-cogs me-2"></i>QR Management
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('view_compartment_qr', compartment_number=1) }}">
                                <i class="fas fa-archive me-2"></i>Compartment 1 QR
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('view_compartment_qr', compartment_number=2) }}">
                                <i class="fas fa-archive me-2"></i>Compartment 2 QR
                            </a></li>
                        </ul>
                    </li>

                    <!-- Reports -->
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('analytics') }}">
                            <i class="fas fa-chart-bar me-2"></i>Reports
                        </a>
                    </li>

                    <!-- Administration (Admin Only) -->
                    {% if current_user.role == 'Administrator' %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-danger" data-bs-toggle="dropdown" href="#" role="button">
                            <i class="fas fa-shield-alt me-2"></i>Administration
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('collaboration') }}">
                                <i class="fas fa-users me-2"></i>User Management
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin_bulk_upload') }}">
                                <i class="fas fa-file-excel me-2"></i>Bulk Upload
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="clearAllData()">
                                <i class="fas fa-trash-alt me-2"></i>Clear All Data
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</nav>

<div class="container-fluid px-4 py-4">
    <!-- Breadcrumb Navigation -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}"><i class="fas fa-home me-1"></i>Home</a></li>
            <li class="breadcrumb-item active" aria-current="page"><i class="fas fa-tachometer-alt me-1"></i>Dashboard</li>
        </ol>
    </nav>

    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="welcome-text">Welcome, {{ current_user.username }}</h1>
                <p class="welcome-subtitle">Taluk Office Digital File Management Dashboard</p>
                <div class="user-role-badge">
                    <span class="badge bg-primary">{{ current_user.role }}</span>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="dashboard-info">
                    <div class="info-item">
                        <i class="fas fa-circle text-success me-2"></i>
                        <span>System Online</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-clock me-2"></i>
                        <span id="currentTime"></span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-calendar me-2"></i>
                        <span id="currentDate"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
        <div class="stats-card">
            <div class="stats-icon primary">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stats-number">{{ stats.total_files }}</div>
            <div class="stats-label">Total Documents</div>
        </div>

        <div class="stats-card">
            <div class="stats-icon success">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stats-number">{{ stats.processed_files }}</div>
            <div class="stats-label">Documents Processed</div>
        </div>

        <div class="stats-card">
            <div class="stats-icon warning">
                <i class="fas fa-qrcode"></i>
            </div>
            <div class="stats-number">{{ stats.qr_codes_generated }}</div>
            <div class="stats-label">QR Codes Generated</div>
        </div>

        <div class="stats-card">
            <div class="stats-icon info">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stats-number">{{ moment().strftime('%H:%M') if moment else 'Active' }}</div>
            <div class="stats-label">System Status</div>
        </div>
    </div>

    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
        <!-- Recent Files -->
        <div class="recent-files">
            <h2 class="section-header">
                <i class="fas fa-file-alt me-2"></i>Recent Files
            </h2>

            {% if files %}
            <ul class="files-list">
                {% for file in files[:5] %}
                <li class="file-item">
                    <div class="file-info">
                        <div class="file-name">{{ file.title }}</div>
                        <div class="file-meta">
                            <span><i class="fas fa-map-marker-alt me-1"></i>Rack {{ file.location.rack_number }}, Row {{ file.location.row_number }}</span>
                            <span><i class="fas fa-calendar me-1"></i>{{ file.created_at.strftime('%Y-%m-%d') }}</span>
                        </div>
                    </div>
                    <div class="file-actions">
                        <a href="{{ url_for('view_file', file_id=file.id) }}" class="action-btn view" title="View File">
                            <i class="fas fa-eye"></i>
                        </a>
                        <button class="action-btn download" title="Download QR Code">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </li>
                {% endfor %}
            </ul>
            {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-folder-open"></i>
                </div>
                <h4>No files yet</h4>
                <p>Start by adding your first file to the system.</p>
                <a href="{{ url_for('add_file') }}" class="btn btn-primary-modern">
                    <i class="fas fa-plus me-2"></i>Add Your First File
                </a>
            </div>
            {% endif %}
        </div>

        <!-- Activity Feed -->
        <div class="activity-feed">
            <h2 class="section-header">
                <i class="fas fa-activity me-2"></i>Recent Activity
            </h2>

            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h4>No recent activity</h4>
                <p>Activity will appear here as you use the system.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Add real-time updates using Socket.IO
const socket = io();

socket.on('file_activity', function(data) {
    // Add new activity to the feed
    const activityFeed = document.querySelector('.activity-feed');
    const newActivity = document.createElement('div');
    newActivity.className = 'activity-item';
    newActivity.innerHTML = `
        <div class="activity-icon ${data.action}">
            <i class="fas fa-${getActionIcon(data.action)}"></i>
        </div>
        <div class="activity-content">
            <p class="activity-text">${data.user} ${data.action} "${data.file_title}"</p>
            <div class="activity-time">Just now</div>
        </div>
    `;

    // Insert at the beginning
    const firstActivity = activityFeed.querySelector('.activity-item');
    if (firstActivity) {
        firstActivity.parentNode.insertBefore(newActivity, firstActivity);
    }

    // Remove last item if more than 5
    const activities = activityFeed.querySelectorAll('.activity-item');
    if (activities.length > 5) {
        activities[activities.length - 1].remove();
    }
});

function getActionIcon(action) {
    const icons = {
        'created': 'plus',
        'viewed': 'eye',
        'scanned': 'qrcode',
        'downloaded': 'download'
    };
    return icons[action] || 'file';
}

// Update time and date
function updateDateTime() {
    const now = new Date();
    const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    };
    const dateOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };

    document.getElementById('currentTime').textContent = now.toLocaleTimeString('en-US', timeOptions);
    document.getElementById('currentDate').textContent = now.toLocaleDateString('en-US', dateOptions);
}

// Add smooth animations
document.addEventListener('DOMContentLoaded', function() {
    // Update time immediately and then every second
    updateDateTime();
    setInterval(updateDateTime, 1000);

    // Animate stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Animate file items
    const fileItems = document.querySelectorAll('.file-item');
    fileItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';

        setTimeout(() => {
            item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, 500 + (index * 100));
    });
});

function clearAllData() {
    if (confirm('⚠️ WARNING: This will permanently delete ALL data from the system including:\n\n• All files and documents\n• All bundles and locations\n• All QR codes\n• All access logs\n• All uploaded files\n\nThis action CANNOT be undone!\n\nAre you absolutely sure you want to proceed?')) {
        if (confirm('This is your final confirmation. Type "DELETE ALL" in the next prompt to proceed.')) {
            const confirmation = prompt('Type "DELETE ALL" to confirm:');
            if (confirmation === 'DELETE ALL') {
                fetch('/admin/clear-all-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
                    }
                })
                .then(response => {
                    if (response.ok) {
                        alert('✅ All data has been cleared successfully. The system has been reset to initial state.');
                        window.location.reload();
                    } else {
                        alert('❌ Error clearing data. Please try again or contact administrator.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('❌ Error clearing data. Please try again or contact administrator.');
                });
            } else {
                alert('Data clearing cancelled - confirmation text did not match.');
            }
        }
    }
}
</script>
{% endblock %}
