// T-Office Service Worker for Offline Functionality
const CACHE_NAME = 'toffice-v1.0.0';
const STATIC_CACHE = 'toffice-static-v1.0.0';
const DYNAMIC_CACHE = 'toffice-dynamic-v1.0.0';

// Files to cache for offline use
const STATIC_FILES = [
    '/',
    '/static/css/main.css',
    '/static/css/components.css',
    '/static/css/responsive.css',
    '/static/js/main.js',
    '/static/js/voice_search.js',
    '/static/js/analytics.js',
    '/static/manifest.json',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
];

// API endpoints that should work offline
const OFFLINE_FALLBACK_PAGES = [
    '/dashboard',
    '/scan',
    '/add_file',
    '/analytics',
    '/collaboration'
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');

    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('Service Worker: Static files cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Error caching static files', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');

    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);

    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }

    // Skip chrome-extension requests
    if (url.protocol === 'chrome-extension:') {
        return;
    }

    event.respondWith(
        caches.match(request)
            .then(cachedResponse => {
                // Return cached version if available
                if (cachedResponse) {
                    return cachedResponse;
                }

                // For navigation requests, try network first, then fallback
                if (request.mode === 'navigate') {
                    return handleNavigationRequest(request);
                }

                // For other requests, try network first, then cache
                return handleResourceRequest(request);
            })
            .catch(error => {
                console.error('Service Worker: Fetch error', error);
                return handleOfflineFallback(request);
            })
    );
});

// Handle navigation requests (page loads)
async function handleNavigationRequest(request) {
    try {
        // Try network first
        const networkResponse = await fetch(request);

        // Cache successful responses
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        // Network failed, try cache
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        // Return offline fallback page
        return getOfflineFallback(request);
    }
}

// Handle resource requests (CSS, JS, images, etc.)
async function handleResourceRequest(request) {
    try {
        // Try network first
        const networkResponse = await fetch(request);

        // Cache successful responses
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        // Network failed, return cached version or fallback
        const cachedResponse = await caches.match(request);
        return cachedResponse || getResourceFallback(request);
    }
}

// Get offline fallback for navigation requests
async function getOfflineFallback(request) {
    const url = new URL(request.url);

    // Check if we have a cached version of a similar page
    for (const page of OFFLINE_FALLBACK_PAGES) {
        if (url.pathname.startsWith(page)) {
            const cachedPage = await caches.match(page);
            if (cachedPage) {
                return cachedPage;
            }
        }
    }

    // Return generic offline page
    return new Response(
        generateOfflinePage(),
        {
            status: 200,
            statusText: 'OK',
            headers: {
                'Content-Type': 'text/html'
            }
        }
    );
}

// Get fallback for resource requests
function getResourceFallback(request) {
    const url = new URL(request.url);

    // Return appropriate fallbacks based on resource type
    if (request.destination === 'image') {
        return new Response(
            generatePlaceholderSVG(),
            {
                headers: {
                    'Content-Type': 'image/svg+xml'
                }
            }
        );
    }

    if (request.destination === 'script') {
        return new Response(
            '// Offline fallback script',
            {
                headers: {
                    'Content-Type': 'application/javascript'
                }
            }
        );
    }

    if (request.destination === 'style') {
        return new Response(
            '/* Offline fallback styles */',
            {
                headers: {
                    'Content-Type': 'text/css'
                }
            }
        );
    }

    return new Response('Offline', { status: 503 });
}

// Generate offline page HTML
function generateOfflinePage() {
    return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Offline - T-Office</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    margin: 0;
                    padding: 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    text-align: center;
                }
                .offline-container {
                    max-width: 500px;
                    padding: 2rem;
                }
                .offline-icon {
                    font-size: 4rem;
                    margin-bottom: 2rem;
                    opacity: 0.8;
                }
                .offline-title {
                    font-size: 2.5rem;
                    font-weight: 700;
                    margin-bottom: 1rem;
                }
                .offline-message {
                    font-size: 1.2rem;
                    margin-bottom: 2rem;
                    opacity: 0.9;
                }
                .offline-btn {
                    background: rgba(255, 255, 255, 0.2);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    color: white;
                    padding: 1rem 2rem;
                    border-radius: 0.5rem;
                    text-decoration: none;
                    font-weight: 600;
                    margin: 0.5rem;
                    cursor: pointer;
                }
            </style>
        </head>
        <body>
            <div class="offline-container">
                <div class="offline-icon">📱</div>
                <h1 class="offline-title">You're Offline</h1>
                <p class="offline-message">
                    Don't worry! T-Office works offline too.
                    Your data will sync when you're back online.
                </p>
                <button class="offline-btn" onclick="window.location.reload()">
                    Try Again
                </button>
                <button class="offline-btn" onclick="history.back()">
                    Go Back
                </button>
            </div>

            <script>
                window.addEventListener('online', () => window.location.reload());
                setInterval(() => {
                    if (navigator.onLine) window.location.reload();
                }, 5000);
            </script>
        </body>
        </html>
    `;
}

// Generate placeholder SVG for images
function generatePlaceholderSVG() {
    return `
        <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#f3f4f6"/>
            <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16"
                  fill="#6b7280" text-anchor="middle" dy=".3em">
                Image unavailable offline
            </text>
        </svg>
    `;
}

console.log('Service Worker: Loaded successfully');