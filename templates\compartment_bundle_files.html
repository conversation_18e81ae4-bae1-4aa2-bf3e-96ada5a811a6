{% extends "base.html" %}

{% block title %}Bundle {{ bundle_number }} Files - Compartment {{ compartment_qr.compartment_number }}{% endblock %}

{% block styles %}
<style>
:root {
    --gov-primary: #1e40af;
    --gov-secondary: #1e3a8a;
    --gov-accent: #f59e0b;
    --gov-success: #059669;
    --gov-warning: #d97706;
    --gov-danger: #dc2626;
    --gov-gray-50: #f8fafc;
    --gov-gray-100: #f1f5f9;
    --gov-gray-200: #e2e8f0;
    --gov-gray-300: #cbd5e1;
    --gov-gray-600: #475569;
    --gov-gray-700: #334155;
    --gov-gray-800: #1e293b;
    --gov-gray-900: #0f172a;
    --gov-white: #ffffff;
    --gov-border: var(--gov-gray-200);
    --gov-text: var(--gov-gray-800);
    --gov-gradient: linear-gradient(135deg, var(--gov-primary), var(--gov-secondary));
    --gov-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --gov-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --gov-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --gov-radius-md: 0.375rem;
    --gov-radius-lg: 0.5rem;
    --gov-radius-xl: 0.75rem;
    --gov-transition: all 0.3s ease;
}

.bundle-header {
    background: var(--gov-gradient);
    color: var(--gov-white);
    padding: 2.5rem 2rem;
    border-radius: var(--gov-radius-xl) var(--gov-radius-xl) 0 0;
    margin-bottom: 0;
    position: relative;
    overflow: hidden;
}

.bundle-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--gov-accent), var(--gov-success), var(--gov-accent));
}

.bundle-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.bundle-subtitle {
    font-size: 1.125rem;
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-weight: 400;
}

.navigation-breadcrumb {
    background: var(--gov-gray-50);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--gov-border);
    font-size: 0.875rem;
}

.breadcrumb-link {
    color: var(--gov-primary);
    text-decoration: none;
    transition: var(--gov-transition);
    font-weight: 600;
}

.breadcrumb-link:hover {
    text-decoration: underline;
    color: var(--gov-secondary);
}

.bundle-stats {
    background: var(--gov-white);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--gov-border);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gov-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--gov-gray-600);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.files-container {
    background: var(--gov-white);
    border-radius: 0 0 var(--gov-radius-xl) var(--gov-radius-xl);
    overflow: hidden;
    box-shadow: var(--gov-shadow-md);
    border: 1px solid var(--gov-border);
}

.files-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.file-item {
    border-bottom: 1px solid var(--gov-border);
    padding: 1.5rem 2rem;
    transition: var(--gov-transition);
}

.file-item:hover {
    background: var(--gov-gray-50);
}

.file-item:last-child {
    border-bottom: none;
}

.file-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.file-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gov-text);
    margin: 0;
    flex: 1;
    margin-right: 1rem;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.action-btn {
    min-width: 120px;
    height: 40px;
    padding: 0 1rem;
    border: none;
    border-radius: var(--gov-radius-md);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: var(--gov-transition);
    cursor: pointer;
    font-size: 0.875rem;
    line-height: 1;
    box-shadow: var(--gov-shadow-sm);
    white-space: nowrap;
    border: 2px solid transparent;
}

.action-btn.primary {
    background: var(--gov-gradient);
    color: var(--gov-white);
    border-color: var(--gov-primary);
}

.action-btn.secondary {
    background: var(--gov-white);
    color: var(--gov-gray-700);
    border-color: var(--gov-border);
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--gov-shadow-md);
    text-decoration: none;
    color: inherit;
}

.action-btn.primary:hover {
    background: var(--gov-secondary);
    border-color: var(--gov-secondary);
}

.action-btn.secondary:hover {
    background: var(--gov-gray-50);
    border-color: var(--gov-primary);
    color: var(--gov-primary);
}

.header-btn {
    min-width: 100px;
    height: 36px;
    background: rgba(255, 255, 255, 0.15);
    color: var(--gov-white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0 1rem;
    border-radius: var(--gov-radius-md);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: var(--gov-transition);
    font-size: 0.75rem;
    line-height: 1;
    white-space: nowrap;
    backdrop-filter: blur(10px);
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    text-decoration: none;
    color: var(--gov-white);
    transform: translateY(-1px);
}

.file-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-group {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.75rem;
    color: var(--gov-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.detail-value {
    font-size: 0.875rem;
    color: var(--gov-text);
    font-weight: 500;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--gray-600);
}

.empty-icon {
    font-size: 4rem;
    color: var(--gray-300);
    margin-bottom: 1rem;
}

.back-button {
    min-width: 180px;
    height: 44px;
    background: var(--gov-white);
    color: var(--gov-gray-700);
    border: 2px solid var(--gov-border);
    padding: 0 1.25rem;
    border-radius: var(--gov-radius-lg);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: var(--gov-transition);
    margin-bottom: 2rem;
    box-shadow: var(--gov-shadow-sm);
    font-size: 0.875rem;
    line-height: 1;
    white-space: nowrap;
}

.back-button:hover {
    background: var(--gov-gray-50);
    border-color: var(--gov-primary);
    color: var(--gov-primary);
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--gov-shadow-md);
}

@media (max-width: 768px) {
    .bundle-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .file-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .file-details {
        grid-template-columns: 1fr;
    }

    .bundle-title {
        font-size: 1.5rem;
    }

    .action-btn {
        min-width: 100px;
        font-size: 0.8rem;
    }

    .back-button {
        min-width: 160px;
        font-size: 0.8rem;
    }

    .header-btn {
        min-width: 80px;
        font-size: 0.7rem;
    }

    .bundle-header {
        padding: 2rem 1.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Back Button -->
    <a href="{{ url_for('view_compartment_qr', compartment_number=compartment_qr.compartment_number) }}" class="back-button">
        <i class="fas fa-arrow-left"></i>Back to Compartment {{ compartment_qr.compartment_number }}
    </a>

    <!-- Bundle Files Container -->
    <div class="files-container">
        <!-- Bundle Header -->
        <div class="bundle-header">
            <div style="display: flex; justify-content: between; align-items: flex-start;">
                <div>
                    <h1 class="bundle-title">
                        <i class="fas fa-layer-group"></i>
                        Bundle {{ bundle_number }} Files
                    </h1>
                    <p class="bundle-subtitle">
                        Compartment {{ compartment_qr.compartment_number }} •
                        Bundle Range: {{ compartment_qr.bundle_range_start }}-{{ compartment_qr.bundle_range_end }}
                    </p>
                </div>
                <div style="display: flex; gap: 0.75rem; flex-shrink: 0;">
                    {% if bundle_record %}
                    <a href="{{ url_for('bundle_detail', bundle_number=bundle_number) }}"
                       class="header-btn"
                       title="View Bundle Management">
                        <i class="fas fa-cog"></i>Manage
                    </a>
                    {% endif %}
                    <a href="{{ url_for('compartment_bundles', compartment_number=compartment_qr.compartment_number) }}"
                       class="header-btn"
                       title="View All Files in Compartment">
                        <i class="fas fa-list"></i>All Files
                    </a>
                </div>
            </div>
        </div>

        <!-- Navigation Breadcrumb -->
        <div class="navigation-breadcrumb">
            <a href="{{ url_for('compartment_qr_management') }}" class="breadcrumb-link">Compartment QR</a>
            <span class="mx-2">›</span>
            <a href="{{ url_for('view_compartment_qr', compartment_number=compartment_qr.compartment_number) }}" class="breadcrumb-link">
                Compartment {{ compartment_qr.compartment_number }}
            </a>
            <span class="mx-2">›</span>
            <span>Bundle {{ bundle_number }}</span>
        </div>

        <!-- Bundle Statistics -->
        <div class="bundle-stats">
            <div class="stat-item">
                <div class="stat-value">{{ total_files }}</div>
                <div class="stat-label">Total Files</div>
            </div>
            {% if bundle_record %}
            <div class="stat-item">
                <div class="stat-value">{{ bundle_record.max_capacity }}</div>
                <div class="stat-label">Max Capacity</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{ ((total_files / bundle_record.max_capacity) * 100) | round(1) }}%</div>
                <div class="stat-label">Utilization</div>
            </div>
            {% endif %}
            <div class="stat-item">
                <div class="stat-value">{{ compartment_qr.compartment_number }}</div>
                <div class="stat-label">Compartment</div>
            </div>
            {% if bundle_record and bundle_record.qr_code_path %}
            <div class="stat-item">
                <div style="display: flex; flex-direction: column; align-items: center; gap: 0.5rem;">
                    <img src="{{ url_for('static', filename='qrcodes/' + bundle_record.qr_code_path) }}"
                         alt="Bundle {{ bundle_number }} QR Code"
                         style="width: 80px; height: 80px; border: 2px solid var(--gray-300); border-radius: var(--radius-md);">
                    <div class="stat-label">Bundle QR Code</div>
                    <a href="{{ url_for('bundle_qr_code', bundle_number=bundle_number) }}"
                       class="action-btn secondary"
                       style="font-size: 0.75rem; padding: 0.25rem 0.5rem;"
                       target="_blank">
                        <i class="fas fa-download"></i>Download
                    </a>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Search within Bundle (if files exist) -->
        {% if bundle_files %}
        <div class="bundle-search" style="padding: 1.5rem 2rem; background: var(--gray-50); border-bottom: 1px solid var(--border-color);">
            <div class="search-container" style="max-width: 500px;">
                <label for="bundle-file-search" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: var(--gray-700);">
                    <i class="fas fa-search me-2"></i>Search Files in Bundle {{ bundle_number }}
                </label>
                <input type="text"
                       id="bundle-file-search"
                       placeholder="Search by RefID, File Number, Category, Subject..."
                       style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: var(--radius-md); font-size: 0.875rem;">
            </div>
        </div>
        {% endif %}

        <!-- Files List -->
        {% if bundle_files %}
        <ul class="files-list">
            {% for file_data in bundle_files %}
            <li class="file-item">
                <div class="file-header">
                    <h3 class="file-title">{{ file_data.file.title }}</h3>
                    <div class="file-actions">
                        <a href="{{ url_for('view_file', file_id=file_data.file.id) }}" class="action-btn primary">
                            <i class="fas fa-eye"></i>View
                        </a>
                        {% if file_data.file.qr_code %}
                        <a href="{{ url_for('get_qrcode', file_id=file_data.file.id) }}" class="action-btn secondary" target="_blank">
                            <i class="fas fa-qrcode"></i>QR Code
                        </a>
                        {% endif %}
                    </div>
                </div>

                <div class="file-details">
                    {% if file_data.excel_data.get('RefID') %}
                    <div class="detail-group">
                        <div class="detail-label">Reference ID</div>
                        <div class="detail-value">{{ file_data.excel_data.get('RefID') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('FILE_NO') %}
                    <div class="detail-group">
                        <div class="detail-label">File Number</div>
                        <div class="detail-value">{{ file_data.excel_data.get('FILE_NO') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('Category') %}
                    <div class="detail-group">
                        <div class="detail-label">Category</div>
                        <div class="detail-value">{{ file_data.excel_data.get('Category') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('hobli_name') %}
                    <div class="detail-group">
                        <div class="detail-label">Hobli</div>
                        <div class="detail-value">{{ file_data.excel_data.get('hobli_name') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('village_name') %}
                    <div class="detail-group">
                        <div class="detail-label">Village</div>
                        <div class="detail-value">{{ file_data.excel_data.get('village_name') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('survey_no') %}
                    <div class="detail-group">
                        <div class="detail-label">Survey Number</div>
                        <div class="detail-value">{{ file_data.excel_data.get('survey_no') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.location %}
                    <div class="detail-group">
                        <div class="detail-label">Physical Location</div>
                        <div class="detail-value">Rack {{ file_data.location.rack_number }}, Row {{ file_data.location.row_number }}, Position {{ file_data.location.position }}</div>
                    </div>
                    {% endif %}
                    <div class="detail-group">
                        <div class="detail-label">Created Date</div>
                        <div class="detail-value">{{ file_data.file.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                    </div>
                </div>
            </li>
            {% endfor %}
        </ul>
        {% else %}
        <!-- Empty State -->
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-folder-open"></i>
            </div>
            <h4>No Files in Bundle {{ bundle_number }}</h4>
            <p>This bundle doesn't contain any files yet. Files will appear here when they are uploaded with Bundle Number {{ bundle_number }}.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Search functionality for files within bundle
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('bundle-file-search');
    const fileItems = document.querySelectorAll('.file-item');

    if (searchInput && fileItems.length > 0) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            let visibleCount = 0;

            fileItems.forEach(function(item) {
                const fileTitle = item.querySelector('.file-title').textContent.toLowerCase();
                const detailValues = Array.from(item.querySelectorAll('.detail-value')).map(el => el.textContent.toLowerCase());
                const allText = [fileTitle, ...detailValues].join(' ');

                if (searchTerm === '' || allText.includes(searchTerm)) {
                    item.style.display = '';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });

            // Update search results indicator
            updateSearchResults(visibleCount, fileItems.length, searchTerm);
        });
    }
});

function updateSearchResults(visibleCount, totalCount, searchTerm) {
    // Remove existing search results indicator
    const existingIndicator = document.querySelector('.search-results-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // Add new search results indicator if searching
    if (searchTerm) {
        const indicator = document.createElement('div');
        indicator.className = 'search-results-indicator';
        indicator.style.cssText = `
            padding: 1rem 2rem;
            background: var(--primary-light);
            border-bottom: 1px solid var(--border-color);
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.875rem;
        `;
        indicator.innerHTML = `
            <i class="fas fa-search me-2"></i>
            Showing ${visibleCount} of ${totalCount} files matching "${searchTerm}"
            ${visibleCount === 0 ? '<span style="color: var(--warning-color); margin-left: 1rem;"><i class="fas fa-exclamation-triangle me-1"></i>No matches found</span>' : ''}
        `;

        const filesList = document.querySelector('.files-list');
        if (filesList) {
            filesList.parentNode.insertBefore(indicator, filesList);
        }
    }
}

// Highlight search terms in results
function highlightSearchTerm(text, searchTerm) {
    if (!searchTerm) return text;

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark style="background: yellow; padding: 0.1rem 0.2rem; border-radius: 0.2rem;">$1</mark>');
}
</script>
{% endblock %}
