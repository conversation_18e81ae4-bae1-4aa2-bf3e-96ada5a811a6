from functools import wraps
from flask import request, session, jsonify, redirect, url_for, flash, current_app
from flask_login import current_user
from datetime import datetime
import json

from models.bulk_data_security import BulkDataAccessSession, BulkDataAccessLog, DataEncryption
from models.file import File
from extensions import db


def get_client_info():
    """Extract client information from request"""
    return {
        'ip_address': request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown')),
        'user_agent': request.headers.get('User-Agent', 'unknown')
    }


def validate_search_criteria(criteria_dict):
    """Validate that search criteria meet minimum security requirements"""
    if not criteria_dict:
        return False, "No search criteria provided"
    
    # Remove empty values
    valid_criteria = {k: v for k, v in criteria_dict.items() if v and str(v).strip()}
    
    # Check minimum criteria count
    if len(valid_criteria) < 2:
        return False, "Minimum 2 search criteria required for bulk data access"
    
    # Validate specific criteria combinations
    has_ref_id = bool(valid_criteria.get('ref_id'))
    has_location = bool(valid_criteria.get('hobli_name') and 
                       valid_criteria.get('village_name') and 
                       valid_criteria.get('survey_no'))
    has_file_info = bool(valid_criteria.get('file_no') and 
                        valid_criteria.get('date_range'))
    
    # At least one of these combinations must be present
    if not (has_ref_id or has_location or has_file_info):
        return False, "Search criteria must include: RefID, or complete location data (hobli+village+survey), or file number with date range"
    
    return True, "Valid search criteria"


def create_bulk_access_session(user_id, search_criteria):
    """Create a new bulk data access session"""
    try:
        # Validate criteria first
        is_valid, message = validate_search_criteria(search_criteria)
        if not is_valid:
            return None, message
        
        # Create new session
        access_session = BulkDataAccessSession(
            user_id=user_id,
            unlock_criteria=search_criteria,
            duration_minutes=30
        )
        
        # Set client info
        client_info = get_client_info()
        access_session.ip_address = client_info['ip_address']
        access_session.user_agent = client_info['user_agent']
        
        db.session.add(access_session)
        db.session.flush()
        
        # Log the session creation
        access_log = BulkDataAccessLog(
            user_id=user_id,
            session_id=access_session.id,
            action='session_created',
            access_granted=True,
            ip_address=client_info['ip_address'],
            user_agent=client_info['user_agent']
        )
        access_log.set_search_criteria(search_criteria)
        
        db.session.add(access_log)
        db.session.commit()
        
        return access_session, "Bulk data access session created successfully"
        
    except Exception as e:
        db.session.rollback()
        return None, f"Error creating access session: {str(e)}"


def get_active_bulk_session(user_id):
    """Get active bulk data access session for user"""
    try:
        session_obj = BulkDataAccessSession.query.filter_by(
            user_id=user_id,
            is_active=True
        ).order_by(BulkDataAccessSession.created_at.desc()).first()
        
        if session_obj and session_obj.is_valid():
            return session_obj
        elif session_obj:
            # Session expired, mark as inactive
            session_obj.revoke()
            db.session.commit()
        
        return None
    except Exception:
        return None


def bulk_data_access_required(f):
    """Decorator to check bulk data access permissions"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('login'))
        
        # Administrators have unrestricted access
        if current_user.role == 'Administrator':
            return f(*args, **kwargs)
        
        # Check for active bulk access session
        active_session = get_active_bulk_session(current_user.id)
        if not active_session:
            flash('Bulk data access requires additional verification. Please provide search criteria.', 'warning')
            return redirect(url_for('bulk_data_unlock'))
        
        # Update session activity
        active_session.record_activity()
        db.session.commit()
        
        # Store session info in request context
        request.bulk_access_session = active_session
        
        return f(*args, **kwargs)
    
    return decorated_function


def secure_search_required(f):
    """Decorator to validate search criteria for bulk data access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('login'))
        
        # Administrators bypass secure search requirements
        if current_user.role == 'Administrator':
            return f(*args, **kwargs)
        
        # Extract search criteria from request
        search_criteria = {}
        if request.method == 'GET':
            search_criteria = {
                'ref_id': request.args.get('ref_id', ''),
                'hobli_name': request.args.get('hobli_name', ''),
                'village_name': request.args.get('village_name', ''),
                'survey_no': request.args.get('survey_no', ''),
                'file_no': request.args.get('file_no', ''),
                'date_range': request.args.get('date_range', '')
            }
        elif request.method == 'POST':
            search_criteria = {
                'ref_id': request.form.get('ref_id', ''),
                'hobli_name': request.form.get('hobli_name', ''),
                'village_name': request.form.get('village_name', ''),
                'survey_no': request.form.get('survey_no', ''),
                'file_no': request.form.get('file_no', ''),
                'date_range': request.form.get('date_range', '')
            }
        
        # Validate search criteria
        is_valid, message = validate_search_criteria(search_criteria)
        if not is_valid:
            # Log failed access attempt
            client_info = get_client_info()
            access_log = BulkDataAccessLog(
                user_id=current_user.id,
                action='access_denied',
                access_granted=False,
                denial_reason=message,
                ip_address=client_info['ip_address'],
                user_agent=client_info['user_agent']
            )
            access_log.set_search_criteria(search_criteria)
            db.session.add(access_log)
            db.session.commit()
            
            flash(f'Access denied: {message}', 'error')
            return redirect(url_for('compartment_search'))
        
        return f(*args, **kwargs)
    
    return decorated_function


def filter_bulk_data_results(results, user_role='Clerk'):
    """Filter and mask bulk uploaded data based on user role"""
    if not results:
        return results
    
    filtered_results = []
    
    for result in results:
        file_obj = result.get('file') if isinstance(result, dict) else result
        
        # Check if this is bulk uploaded data
        if hasattr(file_obj, 'import_batch_id') and file_obj.import_batch_id:
            # This is bulk uploaded data - apply security measures
            if user_role != 'Administrator':
                # For non-Administrators, apply data masking
                masked_result = result.copy() if isinstance(result, dict) else result
                
                # Mask sensitive data in excel_row_data
                if hasattr(file_obj, 'excel_row_data') and file_obj.excel_row_data:
                    try:
                        excel_data = json.loads(file_obj.excel_row_data)
                        masked_data = DataEncryption.mask_sensitive_data(excel_data)
                        # Don't modify the original object, just mark it as protected
                        if isinstance(masked_result, dict):
                            masked_result['is_protected'] = True
                            masked_result['protection_level'] = 'masked'
                    except json.JSONDecodeError:
                        pass
                
                # Add protection indicators
                if isinstance(masked_result, dict):
                    masked_result['is_bulk_data'] = True
                    masked_result['requires_verification'] = True
                
                filtered_results.append(masked_result)
            else:
                # Administrators see full data but with indicators
                admin_result = result.copy() if isinstance(result, dict) else result
                if isinstance(admin_result, dict):
                    admin_result['is_bulk_data'] = True
                    admin_result['is_administrator_view'] = True
                filtered_results.append(admin_result)
        else:
            # Regular uploaded data - no restrictions
            filtered_results.append(result)
    
    return filtered_results


def log_bulk_data_access(file_id, action='viewed', session_id=None):
    """Log access to bulk uploaded data"""
    try:
        file_obj = File.query.get(file_id)
        if not file_obj or not file_obj.import_batch_id:
            return  # Not bulk data
        
        client_info = get_client_info()
        
        # Create enhanced access log
        from models.access_log import AccessLog
        access_log = AccessLog(
            file_id=file_id,
            user_id=current_user.id if current_user.is_authenticated else None,
            action=action,
            is_bulk_data=True,
            access_granted=True,
            access_session_id=session_id,
            ip_address=client_info['ip_address'],
            user_agent=client_info['user_agent']
        )
        
        db.session.add(access_log)
        
        # Also create bulk-specific log
        if session_id:
            bulk_log = BulkDataAccessLog(
                user_id=current_user.id,
                session_id=session_id,
                action=f'file_{action}',
                files_accessed=1,
                access_granted=True,
                ip_address=client_info['ip_address'],
                user_agent=client_info['user_agent']
            )
            db.session.add(bulk_log)
        
        db.session.commit()
        
    except Exception as e:
        current_app.logger.error(f"Error logging bulk data access: {str(e)}")


def check_export_permissions(file_id, user_role='Clerk'):
    """Check if user can export/download bulk uploaded data"""
    try:
        file_obj = File.query.get(file_id)
        if not file_obj:
            return False, "File not found"
        
        # Check if it's bulk uploaded data
        if file_obj.import_batch_id:
            if user_role != 'Administrator':
                return False, "Export of bulk uploaded data is restricted to Administrators only"
        
        return True, "Export permitted"
        
    except Exception as e:
        return False, f"Error checking export permissions: {str(e)}"
