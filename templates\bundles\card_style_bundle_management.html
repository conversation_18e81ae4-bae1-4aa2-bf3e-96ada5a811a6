{% extends "base.html" %}

{% block title %}Card Style Bundle Management - Taluk Office{% endblock %}

{% block content %}
<style>
    /* Card Style Bundle Management */
    .bundle-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 30px 30px;
        position: relative;
        overflow: hidden;
    }

    .bundle-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .bundle-header .container {
        position: relative;
        z-index: 1;
    }

    .bundle-header h1 {
        font-size: 3rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .bundle-header .subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-top: 0.5rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: white;
        border-radius: 25px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 900;
        color: #667eea;
        margin: 0;
        line-height: 1;
    }

    .stat-label {
        color: #6b7280;
        font-size: 1rem;
        font-weight: 600;
        margin-top: 0.5rem;
    }

    .stat-icon {
        font-size: 2.5rem;
        color: #667eea;
        opacity: 0.8;
        float: right;
        margin-top: -3rem;
    }

    .racks-container {
        margin-bottom: 3rem;
    }

    .rack-card {
        background: white;
        border-radius: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 2rem;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .rack-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .rack-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .rack-header:hover::before {
        transform: translateX(100%);
    }

    .rack-title {
        font-size: 2rem;
        font-weight: 800;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .rack-toggle {
        font-size: 1.5rem;
        transition: transform 0.3s ease;
        opacity: 0.8;
    }

    .rack-card.collapsed .rack-toggle {
        transform: rotate(-90deg);
    }

    .rack-card.collapsed .bundles-container {
        display: none;
    }

    .rack-stats {
        display: flex;
        gap: 2rem;
        margin-top: 1rem;
    }

    .rack-stat {
        text-align: center;
    }

    .rack-stat-number {
        font-size: 1.5rem;
        font-weight: 800;
        margin: 0;
    }

    .rack-stat-label {
        font-size: 0.85rem;
        opacity: 0.8;
        margin: 0;
    }

    .bundles-container {
        padding: 2rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .bundles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
    }

    .bundle-card {
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 20px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .bundle-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #10b981, #059669);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .bundle-card:hover::before {
        transform: scaleX(1);
    }

    .bundle-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        border-color: #10b981;
    }

    .bundle-header-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .bundle-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #10b981;
        margin: 0;
    }

    .bundle-stats-mini {
        display: flex;
        gap: 1rem;
        font-size: 0.85rem;
        color: #6b7280;
    }

    .villages-in-bundle {
        margin-top: 1rem;
    }

    .villages-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 1rem;
    }

    .village-card {
        background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
        border: 2px solid #bbf7d0;
        border-radius: 15px;
        padding: 1rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .village-card:hover {
        transform: translateY(-3px);
        border-color: #10b981;
        background: linear-gradient(135deg, #bbf7d0 0%, #a7f3d0 100%);
        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
    }

    .village-name {
        font-size: 1rem;
        font-weight: 700;
        color: #166534;
        margin-bottom: 0.5rem;
    }

    .village-file-count {
        font-size: 0.8rem;
        color: #059669;
        font-weight: 600;
    }

    .search-filters {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .search-filters .form-control {
        border-radius: 15px;
        border: 2px solid #e5e7eb;
        padding: 1rem 1.25rem;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .search-filters .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
    }

    .empty-state {
        text-align: center;
        padding: 4rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 5rem;
        margin-bottom: 1.5rem;
        opacity: 0.3;
        color: #667eea;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .bundles-grid {
            grid-template-columns: 1fr;
        }

        .villages-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }

        .bundle-header h1 {
            font-size: 2.2rem;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
    }
</style>

<!-- Bundle Management Header -->
<div class="bundle-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-layer-group me-3"></i>Card Style Bundle Management</h1>
            <p class="subtitle">Rack → Bundle → Village → Files | Modern Card Interface</p>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ total_racks }}</div>
            <div class="stat-label">Total Racks</div>
            <div class="stat-icon"><i class="fas fa-warehouse"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_bundles }}</div>
            <div class="stat-label">Total Bundles</div>
            <div class="stat-icon"><i class="fas fa-folder"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_villages }}</div>
            <div class="stat-label">Total Villages</div>
            <div class="stat-icon"><i class="fas fa-map-marker-alt"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_files }}</div>
            <div class="stat-label">Total Files</div>
            <div class="stat-icon"><i class="fas fa-file-alt"></i></div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <div class="row">
            <div class="col-md-4">
                <input type="text" class="form-control" id="rackSearch" placeholder="🏢 Search racks..." onkeyup="filterRacks()">
            </div>
            <div class="col-md-4">
                <input type="text" class="form-control" id="bundleSearch" placeholder="📁 Search bundles..." onkeyup="filterBundles()">
            </div>
            <div class="col-md-4">
                <input type="text" class="form-control" id="villageSearch" placeholder="🏘️ Search villages..." onkeyup="filterVillages()">
            </div>
        </div>
    </div>

    <!-- Racks Container -->
    <div class="racks-container">
        {% if rack_hierarchy %}
            {% for rack_no, rack_data in rack_hierarchy.items() %}
            <div class="rack-card" data-rack="{{ rack_no }}">
                <div class="rack-header" onclick="toggleRack('{{ rack_no }}')">
                    <div>
                        <div class="rack-title">
                            <i class="fas fa-chevron-down rack-toggle"></i>
                            <i class="fas fa-warehouse me-2"></i>
                            Rack {{ rack_no }}
                        </div>
                        
                        <div class="rack-stats">
                            <div class="rack-stat">
                                <div class="rack-stat-number">{{ rack_data.total_bundles }}</div>
                                <div class="rack-stat-label">Bundles</div>
                            </div>
                            <div class="rack-stat">
                                <div class="rack-stat-number">{{ rack_data.total_villages }}</div>
                                <div class="rack-stat-label">Villages</div>
                            </div>
                            <div class="rack-stat">
                                <div class="rack-stat-number">{{ rack_data.total_files }}</div>
                                <div class="rack-stat-label">Files</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bundles-container">
                    <div class="bundles-grid">
                        {% for bundle_no, bundle_data in rack_data.bundles.items() %}
                        <div class="bundle-card" data-bundle="{{ bundle_no }}">
                            <div class="bundle-header-info">
                                <div class="bundle-title">
                                    <i class="fas fa-folder me-2"></i>Bundle {{ bundle_no }}
                                </div>
                                <div class="bundle-stats-mini">
                                    <span><i class="fas fa-map-marker-alt me-1"></i>{{ bundle_data.total_villages }} villages</span>
                                    <span><i class="fas fa-file me-1"></i>{{ bundle_data.total_files }} files</span>
                                </div>
                            </div>

                            <div class="villages-in-bundle">
                                <div class="villages-grid">
                                    {% for village_name, village_data in bundle_data.villages.items() %}
                                    <div class="village-card" data-village="{{ village_name }}" onclick="viewVillage('{{ rack_no }}', '{{ bundle_no }}', '{{ village_name }}')">
                                        <div class="village-name">{{ village_name }}</div>
                                        <div class="village-file-count">{{ village_data.file_count }} files</div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <i class="fas fa-layer-group"></i>
                <h3>No Bundle Data Found</h3>
                <p>Upload Excel files with rack, bundle, and village information to see card-style organization.</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
    // Rack toggle functionality
    function toggleRack(rackNo) {
        const rackCard = document.querySelector(`[data-rack="${rackNo}"]`);
        rackCard.classList.toggle('collapsed');
        
        // Save collapsed state to localStorage
        const collapsedRacks = JSON.parse(localStorage.getItem('collapsedRacks') || '[]');
        if (rackCard.classList.contains('collapsed')) {
            if (!collapsedRacks.includes(rackNo)) {
                collapsedRacks.push(rackNo);
            }
        } else {
            const index = collapsedRacks.indexOf(rackNo);
            if (index > -1) {
                collapsedRacks.splice(index, 1);
            }
        }
        localStorage.setItem('collapsedRacks', JSON.stringify(collapsedRacks));
    }
    
    // Village view functionality
    function viewVillage(rackNo, bundleNo, villageName) {
        window.location.href = `/bundles/rack/${rackNo}/bundle/${bundleNo}/village/${encodeURIComponent(villageName)}`;
    }
    
    // Filter racks
    function filterRacks() {
        const searchTerm = document.getElementById('rackSearch').value.toLowerCase();
        const rackCards = document.querySelectorAll('.rack-card');
        
        rackCards.forEach(card => {
            const rackNo = card.dataset.rack.toLowerCase();
            if (rackNo.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    // Filter bundles
    function filterBundles() {
        const searchTerm = document.getElementById('bundleSearch').value.toLowerCase();
        const bundleCards = document.querySelectorAll('.bundle-card');
        
        bundleCards.forEach(card => {
            const bundleNo = card.dataset.bundle.toLowerCase();
            if (bundleNo.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    // Filter villages
    function filterVillages() {
        const searchTerm = document.getElementById('villageSearch').value.toLowerCase();
        const villageCards = document.querySelectorAll('.village-card');
        
        villageCards.forEach(card => {
            const villageName = card.dataset.village.toLowerCase();
            if (villageName.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    // Restore collapsed state on page load
    document.addEventListener('DOMContentLoaded', function() {
        const collapsedRacks = JSON.parse(localStorage.getItem('collapsedRacks') || '[]');
        collapsedRacks.forEach(rackNo => {
            const rackCard = document.querySelector(`[data-rack="${rackNo}"]`);
            if (rackCard) {
                rackCard.classList.add('collapsed');
            }
        });
    });
</script>
{% endblock %}
