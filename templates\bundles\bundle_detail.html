<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bundle {{ bundle.bundle_number }} - T-Office</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .bundle-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }
        
        .bundle-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .info-item {
            text-align: center;
        }
        
        .info-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .info-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .capacity-bar {
            width: 100%;
            height: 12px;
            background: rgba(255,255,255,0.3);
            border-radius: 6px;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .capacity-fill {
            height: 100%;
            background: rgba(255,255,255,0.8);
            transition: width 0.3s ease;
        }
        
        .search-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .files-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .file-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .file-item:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .file-item.bulk-data {
            border-left: 4px solid #ffc107;
            background: linear-gradient(135deg, #fff 0%, #fffbf0 100%);
        }
        
        .file-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .file-title {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 0.5rem;
        }
        
        .file-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .file-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .action-btn {
            padding: 0.25rem 0.75rem;
            border: none;
            border-radius: 4px;
            font-size: 0.8rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .action-btn.primary {
            background: #007bff;
            color: white;
        }
        
        .action-btn.primary:hover {
            background: #0056b3;
            color: white;
        }
        
        .action-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .action-btn.warning:hover {
            background: #e0a800;
            color: #212529;
        }
        
        .action-btn.secondary {
            background: #6c757d;
            color: white;
        }
        
        .action-btn.secondary:hover {
            background: #545b62;
            color: white;
        }
        
        .bulk-data-indicator {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: bold;
            margin-left: 0.5rem;
        }
        
        .protection-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: #856404;
        }
        
        .session-status {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: #155724;
        }
        
        .no-files {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .no-files i {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .bundle-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .bundle-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .bundle-btn.primary {
            background: #007bff;
            color: white;
        }
        
        .bundle-btn.primary:hover {
            background: #0056b3;
            color: white;
        }
        
        .bundle-btn.secondary {
            background: #6c757d;
            color: white;
        }
        
        .bundle-btn.secondary:hover {
            background: #545b62;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('bundle_list') }}">
                <i class="fas fa-archive me-2"></i>Bundle {{ bundle.bundle_number }}
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('bundle_list') }}">
                    <i class="fas fa-arrow-left me-1"></i>All Bundles
                </a>
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Bundle Header -->
        <div class="bundle-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1>
                        <i class="fas fa-archive me-2"></i>
                        Bundle {{ bundle.bundle_number }}
                    </h1>
                    <p class="mb-0">{{ bundle.description or 'Government document bundle' }}</p>
                </div>
                <div class="text-end">
                    <span class="badge bg-light text-dark fs-6">
                        {{ bundle.get_status().replace('_', ' ').title() }}
                    </span>
                </div>
            </div>
            
            <div class="bundle-info-grid">
                <div class="info-item">
                    <div class="info-number">{{ bundle.current_count }}</div>
                    <div class="info-label">Files in Bundle</div>
                </div>
                <div class="info-item">
                    <div class="info-number">{{ bundle.max_capacity }}</div>
                    <div class="info-label">Max Capacity</div>
                </div>
                <div class="info-item">
                    <div class="info-number">{{ "%.1f"|format(bundle.get_capacity_percentage()) }}%</div>
                    <div class="info-label">Capacity Used</div>
                </div>
                {% if bundle.get_compartment_info() %}
                <div class="info-item">
                    <div class="info-number">{{ bundle.get_compartment_info().compartment_number }}</div>
                    <div class="info-label">Compartment</div>
                </div>
                {% endif %}
            </div>
            
            <div class="capacity-bar">
                <div class="capacity-fill" style="width: {{ bundle.get_capacity_percentage() }}%"></div>
            </div>
            
            <div class="bundle-actions">
                {% if bundle.qr_code_path %}
                <a href="{{ url_for('bundle_qr_code', bundle_number=bundle.bundle_number) }}" 
                   class="bundle-btn primary" target="_blank">
                    <i class="fas fa-qrcode me-1"></i>View QR Code
                </a>
                {% endif %}
                <a href="{{ url_for('bundle_search', bundle_number=bundle.bundle_number) }}" 
                   class="bundle-btn secondary">
                    <i class="fas fa-search me-1"></i>Search in Bundle
                </a>
            </div>
        </div>

        <!-- Session Status -->
        {% if active_session %}
        <div class="session-status">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Bulk Data Access Active</strong> - {{ active_session.time_remaining() }} minutes remaining
                </div>
                <div>
                    <button class="btn btn-sm btn-outline-success me-2" onclick="extendSession()">
                        <i class="fas fa-clock me-1"></i>Extend
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="revokeSession()">
                        <i class="fas fa-times me-1"></i>End
                    </button>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Search Section -->
        <div class="search-section">
            <h4><i class="fas fa-search me-2"></i>Search Files in Bundle</h4>
            <form method="GET" action="{{ url_for('bundle_search', bundle_number=bundle.bundle_number) }}">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" 
                           placeholder="Search by title, RefID, file number, or any field..." 
                           value="">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search me-1"></i>Search
                    </button>
                </div>
            </form>
        </div>

        <!-- Files Section -->
        <div class="files-section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4><i class="fas fa-file-alt me-2"></i>Files in Bundle ({{ files|length }})</h4>
                {% if current_user.role == 'Administrator' %}
                <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#reassignModal">
                    <i class="fas fa-exchange-alt me-1"></i>Reassign Files
                </button>
                {% endif %}
            </div>

            {% if files %}
                {% for result in files %}
                <div class="file-item {% if result.is_bulk_data %}bulk-data{% endif %}">
                    {% if result.is_bulk_data %}
                    <div class="bulk-data-indicator">
                        <i class="fas fa-shield-alt"></i>
                        Protected Data
                        {% if result.requires_verification %}
                        <span class="verification-required">Verification Required</span>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <div class="file-header">
                        <div class="file-title">
                            {{ result.file.title }}
                            {% if result.is_bulk_data and not active_session %}
                            <i class="fas fa-lock text-warning ms-2" title="Requires verification"></i>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="file-meta">
                        <strong>File ID:</strong> {{ result.file.id }} |
                        {% if result.file.excel_row_data %}
                        {% set excel_data = result.file.excel_row_data | from_json %}
                        {% if excel_data.RefID %}
                        <strong>RefID:</strong> {{ excel_data.RefID }} |
                        {% endif %}
                        {% if excel_data.FILE_NO %}
                        <strong>File No:</strong> {{ excel_data.FILE_NO }} |
                        {% endif %}
                        {% endif %}
                        <strong>Created:</strong> {{ result.file.created_at.strftime('%Y-%m-%d') }}
                    </div>
                    
                    <div class="file-actions">
                        {% if result.is_bulk_data and result.requires_verification and not active_session %}
                        <a href="{{ url_for('bulk_data_unlock') }}" class="action-btn warning">
                            <i class="fas fa-unlock"></i>Verify Access
                        </a>
                        {% else %}
                        <a href="{{ url_for('view_file', file_id=result.file.id) }}" class="action-btn primary">
                            <i class="fas fa-eye"></i>View
                        </a>
                        {% endif %}
                        {% if result.file.qr_code %}
                        <a href="{{ url_for('get_qrcode', file_id=result.file.id) }}" class="action-btn secondary" target="_blank">
                            <i class="fas fa-qrcode"></i>QR Code
                        </a>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-files">
                    <i class="fas fa-folder-open"></i>
                    <h5>No Files in Bundle</h5>
                    <p>This bundle is currently empty. Files will appear here when they are assigned to Bundle {{ bundle.bundle_number }}.</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Reassign Modal (Administrator only) -->
    {% if current_user.role == 'Administrator' %}
    <div class="modal fade" id="reassignModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reassign File to Different Bundle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="{{ url_for('reassign_file_bundle') }}">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="file_id" class="form-label">File ID</label>
                            <input type="number" class="form-control" id="file_id" name="file_id" required>
                        </div>
                        <div class="mb-3">
                            <label for="new_bundle_number" class="form-label">New Bundle Number (1-40)</label>
                            <input type="number" class="form-control" id="new_bundle_number" name="new_bundle_number" 
                                   min="1" max="40" required>
                        </div>
                        <div class="mb-3">
                            <label for="reason" class="form-label">Reason for Reassignment</label>
                            <textarea class="form-control" id="reason" name="reason" rows="3" 
                                      placeholder="Enter reason for reassignment..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Reassign File</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endif %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Session management functions
        {% if active_session %}
        function extendSession() {
            fetch('/bulk-data-session/extend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to extend session: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error extending session:', error);
                alert('Error extending session');
            });
        }

        function revokeSession() {
            if (confirm('Are you sure you want to end your current verification session?')) {
                fetch('/bulk-data-session/revoke', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Failed to revoke session: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error revoking session:', error);
                    alert('Error revoking session');
                });
            }
        }
        {% endif %}
    </script>
</body>
</html>
