{% extends "base.html" %}

{% block title %}Search Results - T-Office{% endblock %}

{% block styles %}
<style>
.search-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.search-header {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.search-title {
    color: #2c3e50;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 10px 0;
}

.search-subtitle {
    color: #7f8c8d;
    font-size: 16px;
    margin: 0 0 20px 0;
}

.search-form {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.search-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: 15px;
    align-items: end;
}

.form-group {
    margin-bottom: 0;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    outline: none;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.results-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 30px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.results-title {
    color: #2c3e50;
    font-size: 22px;
    font-weight: 600;
    margin: 0;
}

.results-count {
    color: #7f8c8d;
    font-size: 14px;
    background: #f8f9fa;
    padding: 8px 15px;
    border-radius: 20px;
    border: 1px solid #e9ecef;
}

.result-item {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    background: white;
}

.result-item:hover {
    border-color: #3498db;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.1);
    transform: translateY(-2px);
}

.result-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.result-title {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 5px 0;
    flex: 1;
}

.result-badges {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-compartment {
    background: #e3f2fd;
    color: #1976d2;
}

.badge-village {
    background: #f3e5f5;
    color: #7b1fa2;
}

.badge-bundle {
    background: #e8f5e8;
    color: #388e3c;
}

.badge-rack {
    background: #f3e5f5;
    color: #7b1fa2;
}

.meta-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #3498db;
}

.meta-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.meta-section .meta-item {
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
}

.meta-section .meta-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.result-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-size: 14px;
}

.meta-item i {
    color: #3498db;
    width: 16px;
}

.result-description {
    color: #495057;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

.result-actions {
    display: flex;
    gap: 10px;
}

.btn-sm {
    padding: 8px 15px;
    font-size: 12px;
}

.btn-outline-primary {
    background: transparent;
    color: #3498db;
    border: 2px solid #3498db;
}

.btn-outline-primary:hover {
    background: #3498db;
    color: white;
}

.btn-outline-success {
    background: transparent;
    color: #27ae60;
    border: 2px solid #27ae60;
}

.btn-outline-success:hover {
    background: #27ae60;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 60px 30px;
    color: #7f8c8d;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.empty-state h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

/* Browse Section Styles */
.browse-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.browse-header {
    text-align: center;
    margin-bottom: 30px;
}

.browse-header h2 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 24px;
}

.browse-header p {
    color: #7f8c8d;
    margin: 0;
    font-size: 16px;
}

.browse-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    height: 100%;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.browse-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    background: white;
    border-color: #3498db;
}

.browse-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 24px;
    color: white;
}

.browse-card h4 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 18px;
}

.browse-card p {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.5;
}

/* Enhanced Location Display */
.location-highlight {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border: 2px solid #28a745;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
}

.location-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.location-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.location-item.primary {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #007bff;
}

.location-item.secondary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

.location-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.location-icon {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.location-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.location-label {
    font-size: 11px;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.location-value {
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
}

.location-summary {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    color: #856404;
    margin-top: 15px;
}

.location-summary strong {
    color: #495057;
}

@media (max-width: 768px) {
    .search-row {
        grid-template-columns: 1fr;
    }

    .results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .result-meta {
        grid-template-columns: 1fr;
    }

    .result-actions {
        flex-wrap: wrap;
    }

    .browse-section {
        padding: 20px;
    }

    .browse-card {
        margin-bottom: 20px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="search-container">
    <!-- Search Header -->
    <div class="search-header">
        <h1 class="search-title">
            <i class="fas fa-search me-3"></i>Global File Search
        </h1>
        <p class="search-subtitle">Search across all compartments, bundles, and villages</p>
        
        <div style="margin-top: 20px;">
            <a href="{{ url_for('dashboard') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
            <a href="{{ url_for('bundle_list') }}" class="btn btn-primary ms-2">
                <i class="fas fa-archive me-2"></i>Browse Bundles
            </a>
        </div>
    </div>

    <!-- Browse Files Section (like dashboard) -->
    {% if not search_query %}
    <div class="browse-section">
        <div class="browse-header">
            <h2><i class="fas fa-folder-open me-2"></i>Browse Files</h2>
            <p>Explore files by category, location, or use the search below</p>
        </div>

        <div class="browse-cards">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="browse-card">
                        <div class="browse-icon">
                            <i class="fas fa-archive"></i>
                        </div>
                        <h4>Browse by Bundles</h4>
                        <p>Explore files organized in bundles and racks</p>
                        <a href="{{ url_for('bundle_list') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-archive me-1"></i>View Bundles
                        </a>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="browse-card">
                        <div class="browse-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h4>Browse by Village</h4>
                        <p>Find files by village and location</p>
                        <a href="{{ url_for('global_search') }}?village=" class="btn btn-success btn-sm">
                            <i class="fas fa-map-marker-alt me-1"></i>View Villages
                        </a>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="browse-card">
                        <div class="browse-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h4>Browse by Compartment</h4>
                        <p>Access files by compartment organization</p>
                        <a href="{{ url_for('global_search') }}?compartment=1" class="btn btn-info btn-sm">
                            <i class="fas fa-building me-1"></i>View Compartments
                        </a>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="browse-card">
                        <div class="browse-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <h4>QR Code Access</h4>
                        <p>Scan QR codes for instant file access</p>
                        <a href="{{ url_for('scan_qrcode') }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-qrcode me-1"></i>Scan QR
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Search Form -->
    <div class="search-form">
        <form method="GET" action="{{ url_for('global_search') }}">
            <div class="search-row">
                <div class="form-group">
                    <label for="q" class="form-label">Search Query</label>
                    <input type="text" class="form-control" id="q" name="q" 
                           value="{{ search_query }}" 
                           placeholder="Enter title, RefID, file number, village name, etc.">
                </div>
                
                <div class="form-group">
                    <label for="compartment" class="form-label">Compartment</label>
                    <select class="form-control" id="compartment" name="compartment">
                        <option value="">All Compartments</option>
                        <option value="1" {{ 'selected' if compartment_filter == '1' else '' }}>Compartment 1</option>
                        <option value="2" {{ 'selected' if compartment_filter == '2' else '' }}>Compartment 2</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="village" class="form-label">Village</label>
                    <select class="form-control" id="village" name="village">
                        <option value="">All Villages</option>
                        {% for village in villages %}
                        <option value="{{ village }}" {{ 'selected' if village_filter == village else '' }}>{{ village }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-control" id="category" name="category">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category }}" {{ 'selected' if category_filter == category else '' }}>{{ category }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Search Results -->
    <div class="results-section">
        <div class="results-header">
            <h2 class="results-title">
                {% if search_query %}
                    Search Results for "{{ search_query }}"
                {% else %}
                    Search Results
                {% endif %}
            </h2>
            <div class="results-count">
                {{ total_results }} result{{ 's' if total_results != 1 else '' }} found
                {% if results|length < total_results %}
                    (showing first {{ results|length }})
                {% endif %}
            </div>
        </div>

        {% if results %}
            {% for result in results %}
            <div class="result-item">
                <div class="result-header">
                    <h3 class="result-title">{{ result.file.title }}</h3>
                    <div class="result-badges">
                        <span class="badge badge-compartment">
                            <i class="fas fa-building me-1"></i>Compartment {{ result.compartment }}
                        </span>
                        <span class="badge badge-bundle">
                            <i class="fas fa-layer-group me-1"></i>Bundle {{ result.bundle_no }}
                        </span>
                        {% if result.rack_number and result.rack_number != 'N/A' %}
                        <span class="badge badge-rack">
                            <i class="fas fa-archive me-1"></i>Rack {{ result.rack_number }}
                        </span>
                        {% endif %}
                        <span class="badge badge-village">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ result.village }}
                        </span>
                    </div>
                </div>
                
                <div class="result-meta">
                    <!-- File Information -->
                    <div class="meta-section">
                        <h5 class="meta-section-title">
                            <i class="fas fa-file-alt me-1"></i>File Information
                        </h5>
                        <div class="meta-item">
                            <i class="fas fa-id-card"></i>
                            <span>File ID: {{ result.file.id }}</span>
                        </div>
                        {% if result.file.ref_id or result.excel_data.get('RefID') %}
                        <div class="meta-item">
                            <i class="fas fa-hashtag"></i>
                            <span>RefID: {{ result.file.ref_id or result.excel_data.get('RefID') }}</span>
                        </div>
                        {% endif %}
                        {% if result.file.file_no or result.excel_data.get('FILE_NO') %}
                        <div class="meta-item">
                            <i class="fas fa-file-alt"></i>
                            <span>File No: {{ result.file.file_no or result.excel_data.get('FILE_NO') }}</span>
                        </div>
                        {% endif %}
                        {% if result.file.category or result.excel_data.get('Category') %}
                        <div class="meta-item">
                            <i class="fas fa-tag"></i>
                            <span>Category: {{ result.file.category or result.excel_data.get('Category') }}</span>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Physical Location Information -->
                    <div class="meta-section location-highlight">
                        <h5 class="meta-section-title">
                            <i class="fas fa-map-marker-alt me-1"></i>Physical Location
                        </h5>
                        <div class="location-grid">
                            <div class="location-item primary">
                                <div class="location-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="location-details">
                                    <span class="location-label">Compartment</span>
                                    <span class="location-value">{{ result.compartment }}</span>
                                </div>
                            </div>
                            <div class="location-item primary">
                                <div class="location-icon">
                                    <i class="fas fa-archive"></i>
                                </div>
                                <div class="location-details">
                                    <span class="location-label">Rack</span>
                                    <span class="location-value">{{ result.rack_number if result.rack_number != 'N/A' else 'Not Set' }}</span>
                                </div>
                            </div>
                            <div class="location-item primary">
                                <div class="location-icon">
                                    <i class="fas fa-layer-group"></i>
                                </div>
                                <div class="location-details">
                                    <span class="location-label">Bundle</span>
                                    <span class="location-value">{{ result.bundle_no if result.bundle_no != 'N/A' else 'Not Set' }}</span>
                                </div>
                            </div>
                            {% if result.location %}
                            <div class="location-item secondary">
                                <div class="location-icon">
                                    <i class="fas fa-th"></i>
                                </div>
                                <div class="location-details">
                                    <span class="location-label">Position</span>
                                    <span class="location-value">Row {{ result.location.row_number }}, Pos {{ result.location.position }}</span>
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Quick Location Summary -->
                        <div class="location-summary">
                            <i class="fas fa-route me-2"></i>
                            <strong>Find at:</strong>
                            Compartment {{ result.compartment }} →
                            Rack {{ result.rack_number if result.rack_number != 'N/A' else '?' }} →
                            Bundle {{ result.bundle_no if result.bundle_no != 'N/A' else '?' }}
                            {% if result.location %} → Row {{ result.location.row_number }}, Position {{ result.location.position }}{% endif %}
                        </div>

                        {% if result.file.survey_no or result.excel_data.get('survey_no') %}
                        <div class="meta-item">
                            <i class="fas fa-map"></i>
                            <span>Survey: {{ result.file.survey_no or result.excel_data.get('survey_no') }}</span>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Geographic Information -->
                    {% if result.file.village_name or result.file.hobli_name or result.excel_data.get('village_name_en') or result.excel_data.get('hobli_name_en') %}
                    <div class="meta-section">
                        <h5 class="meta-section-title">
                            <i class="fas fa-globe me-1"></i>Geographic Details
                        </h5>
                        {% if result.file.village_name or result.excel_data.get('village_name_en') or result.excel_data.get('village_name') %}
                        <div class="meta-item">
                            <i class="fas fa-home"></i>
                            <span>Village: {{ result.file.village_name or result.excel_data.get('village_name_en') or result.excel_data.get('village_name') }}</span>
                        </div>
                        {% endif %}
                        {% if result.file.hobli_name or result.excel_data.get('hobli_name_en') or result.excel_data.get('hobli_name') %}
                        <div class="meta-item">
                            <i class="fas fa-map-pin"></i>
                            <span>Hobli: {{ result.file.hobli_name or result.excel_data.get('hobli_name_en') or result.excel_data.get('hobli_name') }}</span>
                        </div>
                        {% endif %}
                        {% if result.excel_data.get('taluk_name_en') or result.excel_data.get('taluk_name') %}
                        <div class="meta-item">
                            <i class="fas fa-location-dot"></i>
                            <span>Taluk: {{ result.excel_data.get('taluk_name_en') or result.excel_data.get('taluk_name') }}</span>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <span>{{ result.file.created_at.strftime('%Y-%m-%d') if result.file.created_at else 'N/A' }}</span>
                    </div>
                </div>
                
                {% if result.file.description %}
                <div class="result-description">
                    {{ result.file.description[:200] }}{% if result.file.description|length > 200 %}...{% endif %}
                </div>
                {% endif %}
                
                <div class="result-actions">
                    <a href="{{ url_for('view_file', file_id=result.file.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>View File
                    </a>
                    {% if result.bundle_no and result.bundle_no != 'N/A' %}
                    <a href="{{ url_for('bundle_detail', bundle_no=result.bundle_no) }}"
                       class="btn btn-outline-success btn-sm">
                        <i class="fas fa-layer-group me-1"></i>View Bundle {{ result.bundle_no }}
                    </a>
                    {% endif %}
                    {% if result.file.village_name and result.bundle_no %}
                    <a href="{{ url_for('village_detail', bundle_no=result.bundle_no, village_name=result.file.village_name) }}"
                       class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-map-marker-alt me-1"></i>View Village
                    </a>
                    {% endif %}
                    <a href="{{ url_for('compartment_bundles', compartment_number=result.compartment) }}"
                       class="btn btn-outline-info btn-sm">
                        <i class="fas fa-building me-1"></i>Compartment {{ result.compartment }}
                    </a>
                    {% if result.rack_number and result.rack_number != 'N/A' %}
                    <a href="{{ url_for('compartment_bundles', compartment_number=result.compartment) }}#rack-{{ result.rack_number }}"
                       class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-archive me-1"></i>Rack {{ result.rack_number }}
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-search"></i>
                </div>
                {% if search_query %}
                    <h3>No Results Found</h3>
                    <p>No files match your search criteria. Try different keywords or filters.</p>
                {% else %}
                    <h3>Enter Search Terms</h3>
                    <p>Use the search form above to find files across all compartments and bundles.</p>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-submit form when filters change
document.querySelectorAll('#compartment, #village, #category').forEach(select => {
    select.addEventListener('change', function() {
        // Only auto-submit if there's a search query
        if (document.getElementById('q').value.trim()) {
            this.form.submit();
        }
    });
});

// Add loading state to search button
document.querySelector('form').addEventListener('submit', function() {
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
    submitBtn.disabled = true;
    
    // Re-enable after 10 seconds in case of issues
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});
</script>
{% endblock %}
