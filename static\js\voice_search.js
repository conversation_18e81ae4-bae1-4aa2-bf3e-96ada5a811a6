// Enhanced Voice Search for T-Office with Text-to-Speech
class VoiceSearch {
    constructor() {
        this.recognition = null;
        this.synthesis = null;
        this.isListening = false;
        this.isSpeaking = false;
        this.commands = {
            'search': this.handleSearchCommand.bind(this),
            'find': this.handleSearchCommand.bind(this),
            'open': this.handleOpenCommand.bind(this),
            'scan': this.handleScanCommand.bind(this),
            'add': this.handleAddCommand.bind(this),
            'create': this.handleAddCommand.bind(this),
            'dashboard': this.handleDashboardCommand.bind(this),
            'analytics': this.handleAnalyticsCommand.bind(this),
            'help': this.handleHelpCommand.bind(this),
            'where': this.handleWhereCommand.bind(this),
            'how': this.handleHowCommand.bind(this),
            'what': this.handleWhatCommand.bind(this),
            'show': this.handleShowCommand.bind(this),
            'go': this.handleGoCommand.bind(this),
            'navigate': this.handleNavigateCommand.bind(this),
            'stop': this.handleStopCommand.bind(this),
            'quiet': this.handleStopCommand.bind(this),
            'silence': this.handleStopCommand.bind(this)
        };
        this.init();
    }

    init() {
        // Initialize Speech Recognition
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            this.setupRecognition();
        } else {
            console.warn('Speech recognition not supported');
            this.hideVoiceButton();
        }

        // Initialize Speech Synthesis
        if ('speechSynthesis' in window) {
            this.synthesis = window.speechSynthesis;
            this.setupSynthesis();
        } else {
            console.warn('Speech synthesis not supported');
        }

        this.setupEventListeners();
    }

    setupSynthesis() {
        // Wait for voices to load
        if (this.synthesis.getVoices().length === 0) {
            this.synthesis.addEventListener('voiceschanged', () => {
                this.selectVoice();
            });
        } else {
            this.selectVoice();
        }
    }

    selectVoice() {
        const voices = this.synthesis.getVoices();
        // Prefer English voices
        this.selectedVoice = voices.find(voice =>
            voice.lang.startsWith('en') && voice.name.includes('Google')
        ) || voices.find(voice =>
            voice.lang.startsWith('en')
        ) || voices[0];
    }

    setupRecognition() {
        this.recognition.continuous = false;
        this.recognition.interimResults = false;
        this.recognition.lang = 'en-US';

        this.recognition.onstart = () => {
            this.isListening = true;
            this.showVoiceIndicator();
            this.updateVoiceButton(true);
        };

        this.recognition.onend = () => {
            this.isListening = false;
            this.hideVoiceIndicator();
            this.updateVoiceButton(false);
        };

        this.recognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript.toLowerCase().trim();
            this.processVoiceCommand(transcript);
        };

        this.recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            this.isListening = false;
            this.hideVoiceIndicator();
            this.updateVoiceButton(false);

            if (event.error === 'not-allowed') {
                this.showNotification('Microphone access denied', 'warning');
            } else {
                this.showNotification('Voice recognition error', 'danger');
            }
        };
    }

    setupEventListeners() {
        const voiceBtn = document.getElementById('voiceSearchBtn');
        if (voiceBtn) {
            voiceBtn.addEventListener('click', () => {
                this.toggleVoiceSearch();
            });
        }

        // Keyboard shortcut for voice search
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'm') {
                e.preventDefault();
                this.toggleVoiceSearch();
            }
        });
    }

    toggleVoiceSearch() {
        if (this.isSpeaking) {
            this.stopSpeaking();
        } else if (this.isListening) {
            this.stopListening();
        } else {
            this.startListening();
        }
    }

    startListening() {
        if (!this.recognition) {
            this.showNotification('Voice search not available', 'warning');
            return;
        }

        try {
            this.recognition.start();
            this.showNotification('Listening... Speak your command', 'info', 2000);
        } catch (error) {
            console.error('Error starting voice recognition:', error);
            this.showNotification('Could not start voice search', 'danger');
        }
    }

    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
        }
    }

    processVoiceCommand(transcript) {
        console.log('Voice command:', transcript);

        // Enhanced command parsing for natural language
        const normalizedTranscript = transcript.toLowerCase().trim();

        // Check for question patterns
        if (this.isQuestion(normalizedTranscript)) {
            this.handleQuestionCommand(normalizedTranscript);
            return;
        }

        // Find matching command
        const words = normalizedTranscript.split(' ');
        const command = words[0];
        const params = words.slice(1).join(' ');

        if (this.commands[command]) {
            this.commands[command](params, transcript);
        } else {
            // Check for partial matches or synonyms
            const matchedCommand = this.findBestCommandMatch(normalizedTranscript);
            if (matchedCommand) {
                this.commands[matchedCommand.command](matchedCommand.params, transcript);
            } else {
                // Default to search if no specific command found
                this.handleSearchCommand(transcript, transcript);
            }
        }
    }

    isQuestion(transcript) {
        const questionWords = ['where', 'how', 'what', 'when', 'why', 'which', 'who'];
        const questionMarkers = ['?', 'where is', 'how do', 'what is', 'how to'];

        return questionWords.some(word => transcript.startsWith(word)) ||
               questionMarkers.some(marker => transcript.includes(marker)) ||
               transcript.endsWith('?');
    }

    findBestCommandMatch(transcript) {
        const synonyms = {
            'search': ['find', 'look for', 'locate', 'search for'],
            'open': ['go to', 'navigate to', 'show me', 'take me to'],
            'scan': ['qr', 'scanner', 'scan code'],
            'help': ['assist', 'guide', 'support', 'commands']
        };

        for (const [command, syns] of Object.entries(synonyms)) {
            for (const syn of syns) {
                if (transcript.includes(syn)) {
                    const params = transcript.replace(syn, '').trim();
                    return { command, params };
                }
            }
        }
        return null;
    }

    // Command handlers
    handleSearchCommand(query, fullTranscript) {
        if (!query.trim()) {
            this.speak('Please specify what to search for');
            this.showNotification('Please specify what to search for', 'warning');
            return;
        }

        const searchInput = document.getElementById('globalSearch');
        if (searchInput) {
            searchInput.value = query;
            searchInput.dispatchEvent(new Event('input'));
            searchInput.focus();
        }

        this.speak(`Searching for ${query}`);
        this.showNotification(`Searching for "${query}"`, 'info');
        this.trackVoiceCommand('search', { query });

        // Perform the search and provide voice feedback
        this.performSearchWithVoiceFeedback(query);
    }

    async performSearchWithVoiceFeedback(query) {
        try {
            const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
            const data = await response.json();

            if (data.results && data.results.length > 0) {
                const count = data.results.length;
                this.speak(`Found ${count} result${count > 1 ? 's' : ''} for ${query}`);
            } else {
                this.speak(`No results found for ${query}. Try different keywords.`);
            }
        } catch (error) {
            this.speak('Sorry, there was an error performing the search');
            console.error('Search error:', error);
        }
    }

    handleOpenCommand(target, fullTranscript) {
        const routes = {
            'dashboard': '/dashboard',
            'files': '/dashboard',
            'analytics': '/analytics',
            'scan': '/scan',
            'scanner': '/scan',
            'collaboration': '/collaboration',
            'bundles': '/dashboard',
            'qr': '/scan',
            'search': '/dashboard'
        };

        const normalizedTarget = target.toLowerCase().trim();

        if (routes[normalizedTarget]) {
            this.speak(`Opening ${normalizedTarget}`);
            window.location.href = routes[normalizedTarget];
            this.showNotification(`Opening ${normalizedTarget}`, 'success');
        } else {
            this.speak(`I don't know how to open ${target}. Try saying dashboard, analytics, scan, or bundles.`);
            this.showNotification(`Don't know how to open "${target}"`, 'warning');
        }

        this.trackVoiceCommand('open', { target });
    }

    handleScanCommand(params, fullTranscript) {
        this.speak('Opening QR scanner');
        window.location.href = '/scan';
        this.showNotification('Opening QR scanner', 'success');
        this.trackVoiceCommand('scan');
    }

    handleAddCommand(params, fullTranscript) {
        this.speak('Opening add file page');
        window.location.href = '/add_file';
        this.showNotification('Opening add file page', 'success');
        this.trackVoiceCommand('add');
    }

    handleDashboardCommand(params, fullTranscript) {
        this.speak('Opening dashboard');
        window.location.href = '/dashboard';
        this.showNotification('Opening dashboard', 'success');
        this.trackVoiceCommand('dashboard');
    }

    handleAnalyticsCommand(params, fullTranscript) {
        this.speak('Opening analytics');
        window.location.href = '/analytics';
        this.showNotification('Opening analytics', 'success');
        this.trackVoiceCommand('analytics');
    }

    handleHelpCommand(params, fullTranscript) {
        this.speak('Here are the available voice commands. Check the popup for details.');
        this.showVoiceHelp();
        this.trackVoiceCommand('help');
    }

    // New command handlers for questions and navigation
    handleQuestionCommand(transcript) {
        if (transcript.includes('where') && transcript.includes('search')) {
            this.speak('You can search files using the search bar at the top, or go to File Operations and click Search Files');
        } else if (transcript.includes('where') && transcript.includes('bundle')) {
            this.speak('Bundles are in Bundle Management. You can access compartment 1 bundles 1 to 400, or compartment 2 bundles 401 to 800');
        } else if (transcript.includes('where') && transcript.includes('qr')) {
            this.speak('QR operations are in the QR Operations menu. You can scan QR codes or manage QR codes for compartments');
        } else if (transcript.includes('how') && transcript.includes('search')) {
            this.speak('To search, you can type in the search bar, use voice search by clicking the microphone, or go to File Operations and click Search Files');
        } else if (transcript.includes('what') && transcript.includes('command')) {
            this.speak('Available commands include: search, open dashboard, open analytics, scan QR, add file, and help. You can also ask where is something or how to do something');
        } else {
            this.speak('I can help you navigate the system. Try asking where is search, where are bundles, or how to search files');
        }
        this.trackVoiceCommand('question', { transcript });
    }

    handleWhereCommand(params, fullTranscript) {
        if (params.includes('search')) {
            this.speak('Search is available in the top navigation bar, or go to File Operations menu and click Search Files');
        } else if (params.includes('bundle')) {
            this.speak('Bundles are in the Bundle Management menu in the dashboard navigation');
        } else if (params.includes('qr')) {
            this.speak('QR operations are in the QR Operations menu in the dashboard');
        } else {
            this.speak('What are you looking for? I can help you find search, bundles, QR operations, or other features');
        }
    }

    handleHowCommand(params, fullTranscript) {
        if (params.includes('search')) {
            this.speak('You can search by typing in the search bar, clicking the microphone for voice search, or using the File Operations menu');
        } else if (params.includes('add')) {
            this.speak('To add files, go to File Operations menu and click Add Document or Upload Excel');
        } else {
            this.speak('What would you like to know how to do? I can help with searching, adding files, or navigating the system');
        }
    }

    handleWhatCommand(params, fullTranscript) {
        if (params.includes('command')) {
            this.speak('Available commands include search, open, scan, add file, help, and navigation questions');
        } else {
            this.speak('What would you like to know about? I can explain commands, features, or help you navigate');
        }
    }

    handleShowCommand(params, fullTranscript) {
        this.handleOpenCommand(params, fullTranscript);
    }

    handleGoCommand(params, fullTranscript) {
        this.handleOpenCommand(params, fullTranscript);
    }

    handleNavigateCommand(params, fullTranscript) {
        this.handleOpenCommand(params, fullTranscript);
    }

    handleStopCommand(params, fullTranscript) {
        this.stopSpeaking();
        this.stopListening();
        this.speak('Voice assistant stopped');
        this.trackVoiceCommand('stop');
    }

    // UI methods
    showVoiceIndicator() {
        let indicator = document.getElementById('voiceIndicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'voiceIndicator';
            indicator.className = 'voice-indicator';
            document.body.appendChild(indicator);
        }

        indicator.innerHTML = '<i class="fas fa-microphone me-2"></i>Listening...';
        indicator.classList.add('show');
    }

    hideVoiceIndicator() {
        const indicator = document.getElementById('voiceIndicator');
        if (indicator) {
            indicator.classList.remove('show');
        }
    }

    showSpeakingIndicator() {
        let indicator = document.getElementById('voiceIndicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'voiceIndicator';
            indicator.className = 'voice-indicator';
            document.body.appendChild(indicator);
        }

        indicator.innerHTML = '<i class="fas fa-volume-up me-2"></i>Speaking...';
        indicator.classList.add('show');
    }

    updateVoiceButton(isListening) {
        const voiceBtn = document.getElementById('voiceSearchBtn');
        if (voiceBtn) {
            if (isListening) {
                voiceBtn.classList.add('listening');
                voiceBtn.classList.remove('speaking');
                voiceBtn.innerHTML = '<i class="fas fa-stop"></i>';
                voiceBtn.title = 'Stop listening';
            } else {
                voiceBtn.classList.remove('listening');
                if (!this.isSpeaking) {
                    voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                    voiceBtn.title = 'Voice search (Ctrl+M)';
                }
            }
        }
    }

    updateVoiceButtonSpeaking(isSpeaking) {
        const voiceBtn = document.getElementById('voiceSearchBtn');
        if (voiceBtn) {
            if (isSpeaking) {
                voiceBtn.classList.add('speaking');
                voiceBtn.classList.remove('listening');
                voiceBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
                voiceBtn.title = 'Speaking...';
            } else {
                voiceBtn.classList.remove('speaking');
                if (!this.isListening) {
                    voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                    voiceBtn.title = 'Voice search (Ctrl+M)';
                }
            }
        }
    }

    hideVoiceButton() {
        const voiceBtn = document.getElementById('voiceSearchBtn');
        if (voiceBtn) {
            voiceBtn.style.display = 'none';
        }
    }

    // Text-to-Speech functionality
    speak(text, options = {}) {
        if (!this.synthesis || !text) return;

        // Stop any current speech
        this.synthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);

        // Configure voice settings
        if (this.selectedVoice) {
            utterance.voice = this.selectedVoice;
        }

        utterance.rate = options.rate || 0.9;
        utterance.pitch = options.pitch || 1;
        utterance.volume = options.volume || 0.8;

        // Event handlers
        utterance.onstart = () => {
            this.isSpeaking = true;
            this.updateVoiceButtonSpeaking(true);
            this.showSpeakingIndicator();
        };

        utterance.onend = () => {
            this.isSpeaking = false;
            this.updateVoiceButtonSpeaking(false);
            this.hideVoiceIndicator();
        };

        utterance.onerror = (event) => {
            console.error('Speech synthesis error:', event);
            this.isSpeaking = false;
            this.updateVoiceButtonSpeaking(false);
            this.hideVoiceIndicator();
        };

        this.synthesis.speak(utterance);
    }

    stopSpeaking() {
        if (this.synthesis) {
            this.synthesis.cancel();
            this.isSpeaking = false;
            this.updateVoiceButtonSpeaking(false);
        }
    }

    showNotification(message, type = 'info', duration = 3000) {
        if (window.TOffice && window.TOffice.showNotification) {
            window.TOffice.showNotification(message, type, duration);
        }
    }

    trackVoiceCommand(command, data = {}) {
        if (window.TOffice && window.TOffice.trackEvent) {
            window.TOffice.trackEvent('voice_command', { command, ...data });
        }
    }

    showVoiceHelp() {
        this.speak('Here are the available voice commands');

        const helpModal = document.createElement('div');
        helpModal.className = 'modal fade';
        helpModal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-microphone me-2"></i>Voice Assistant Commands</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-search me-2"></i>Search Commands:</h6>
                                <ul class="list-unstyled mb-3">
                                    <li><strong>"Search [query]"</strong> - Search for files</li>
                                    <li><strong>"Find [query]"</strong> - Search for files</li>
                                    <li><strong>"Look for [query]"</strong> - Search for files</li>
                                </ul>

                                <h6><i class="fas fa-navigation me-2"></i>Navigation Commands:</h6>
                                <ul class="list-unstyled mb-3">
                                    <li><strong>"Open dashboard"</strong> - Go to dashboard</li>
                                    <li><strong>"Open analytics"</strong> - Go to analytics</li>
                                    <li><strong>"Go to bundles"</strong> - Open bundle management</li>
                                    <li><strong>"Scan"</strong> - Open QR scanner</li>
                                    <li><strong>"Add file"</strong> - Go to add file page</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-question-circle me-2"></i>Question Commands:</h6>
                                <ul class="list-unstyled mb-3">
                                    <li><strong>"Where is search?"</strong> - Find search options</li>
                                    <li><strong>"Where are bundles?"</strong> - Find bundle management</li>
                                    <li><strong>"How to search?"</strong> - Learn search methods</li>
                                    <li><strong>"What commands?"</strong> - List all commands</li>
                                </ul>

                                <h6><i class="fas fa-cog me-2"></i>System Commands:</h6>
                                <ul class="list-unstyled mb-3">
                                    <li><strong>"Help"</strong> - Show this help</li>
                                    <li><strong>"Stop"</strong> - Stop listening/speaking</li>
                                </ul>
                            </div>
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>Tips:</strong> You can ask natural questions like "Where is the search option?" or "How do I add files?"
                            Use <kbd>Ctrl+M</kbd> to start voice search quickly.
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(helpModal);
        const modal = new bootstrap.Modal(helpModal);
        modal.show();

        helpModal.addEventListener('hidden.bs.modal', () => {
            helpModal.remove();
        });
    }
}

// Initialize voice search when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.voiceSearch = new VoiceSearch();
});

// Add CSS for voice search
const voiceStyle = document.createElement('style');
voiceStyle.textContent = `
    .search-btn.listening {
        background: var(--danger-color) !important;
        animation: pulse 1s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
        }
    }
`;
document.head.appendChild(voiceStyle);