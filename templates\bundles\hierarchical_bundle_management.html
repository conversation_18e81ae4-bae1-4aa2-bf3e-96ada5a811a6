{% extends "base.html" %}

{% block title %}Hierarchical Bundle Management - Taluk Office{% endblock %}

{% block content %}
<style>
    /* Hierarchical Bundle Management Styles */
    .bundle-management-header {
        background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 3rem;
        border-radius: 0 0 30px 30px;
        position: relative;
        overflow: hidden;
    }

    .bundle-management-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .bundle-management-header .container {
        position: relative;
        z-index: 1;
    }

    .bundle-management-header h1 {
        font-size: 3rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .bundle-management-header .subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-top: 0.5rem;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: white;
        border-radius: 25px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.1);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    .stat-card .stat-number {
        font-size: 3rem;
        font-weight: 900;
        color: #1e3a8a;
        margin: 0;
        line-height: 1;
    }

    .stat-card .stat-label {
        color: #6b7280;
        font-size: 1rem;
        font-weight: 600;
        margin-top: 0.5rem;
    }

    .stat-card .stat-icon {
        font-size: 2.5rem;
        color: #3b82f6;
        opacity: 0.8;
        float: right;
        margin-top: -3rem;
    }

    .racks-container {
        margin-bottom: 3rem;
    }

    .rack-section {
        background: white;
        border-radius: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 2rem;
        border: 1px solid rgba(59, 130, 246, 0.1);
    }

    .rack-header {
        background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%);
        color: white;
        padding: 2rem;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .rack-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .rack-header:hover::before {
        transform: translateX(100%);
    }

    .rack-title-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .rack-title {
        font-size: 2rem;
        font-weight: 800;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .rack-toggle {
        font-size: 1.5rem;
        transition: transform 0.3s ease;
        opacity: 0.8;
    }

    .rack-section.collapsed .rack-toggle {
        transform: rotate(-90deg);
    }

    .rack-section.collapsed .bundles-container {
        display: none;
    }

    .rack-stats {
        display: flex;
        gap: 2rem;
        margin-top: 1rem;
    }

    .rack-stat {
        text-align: center;
    }

    .rack-stat-number {
        font-size: 1.5rem;
        font-weight: 800;
        margin: 0;
    }

    .rack-stat-label {
        font-size: 0.85rem;
        opacity: 0.8;
        margin: 0;
    }

    .bundles-container {
        padding: 2rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .bundles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    .bundle-card {
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 20px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .bundle-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #059669, #10b981);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .bundle-card:hover::before {
        transform: scaleX(1);
    }

    .bundle-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        border-color: #059669;
    }

    .bundle-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .bundle-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #059669;
        margin: 0;
        flex: 1;
    }

    .bundle-file-count {
        background: #059669;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .bundle-village {
        color: #6b7280;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .bundle-categories {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .category-tag {
        background: linear-gradient(135deg, #dcfce7, #bbf7d0);
        color: #166534;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .bundle-actions {
        display: flex;
        gap: 0.75rem;
        margin-top: 1rem;
    }

    .btn-bundle-action {
        background: linear-gradient(135deg, #059669, #10b981);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 10px;
        font-size: 0.85rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-bundle-action:hover {
        background: linear-gradient(135deg, #047857, #059669);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(5, 150, 105, 0.3);
    }

    .search-filters {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(59, 130, 246, 0.1);
    }

    .search-filters .form-control {
        border-radius: 15px;
        border: 2px solid #e5e7eb;
        padding: 1rem 1.25rem;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .search-filters .form-control:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        transform: translateY(-2px);
    }

    .empty-state {
        text-align: center;
        padding: 4rem;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 5rem;
        margin-bottom: 1.5rem;
        opacity: 0.3;
        color: #3b82f6;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .bundles-grid {
            grid-template-columns: 1fr;
        }

        .bundle-management-header h1 {
            font-size: 2.2rem;
        }

        .stats-cards {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .rack-stats {
            gap: 1rem;
        }
    }
</style>

<!-- Bundle Management Header -->
<div class="bundle-management-header">
    <div class="container">
        <div class="text-center">
            <h1><i class="fas fa-sitemap me-3"></i>Hierarchical Bundle Management</h1>
            <p class="subtitle">Racks → Bundles → Files | Organized by Village Names</p>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-number">{{ total_racks }}</div>
            <div class="stat-label">Total Racks</div>
            <div class="stat-icon"><i class="fas fa-warehouse"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_bundles }}</div>
            <div class="stat-label">Total Bundles</div>
            <div class="stat-icon"><i class="fas fa-folder"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_files }}</div>
            <div class="stat-label">Total Files</div>
            <div class="stat-icon"><i class="fas fa-file-alt"></i></div>
        </div>

        <div class="stat-card">
            <div class="stat-number">{{ total_villages }}</div>
            <div class="stat-label">Villages</div>
            <div class="stat-icon"><i class="fas fa-map-marker-alt"></i></div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <div class="row">
            <div class="col-md-6">
                <input type="text" class="form-control" id="rackSearch" placeholder="🏢 Search racks..." onkeyup="filterRacks()">
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control" id="bundleSearch" placeholder="📁 Search bundles..." onkeyup="filterBundles()">
            </div>
        </div>
    </div>

    <!-- Racks Container -->
    <div class="racks-container">
        {% if rack_hierarchy %}
            {% for rack_no, rack_data in rack_hierarchy.items() %}
            <div class="rack-section" data-rack="{{ rack_no }}">
                <div class="rack-header" onclick="toggleRack('{{ rack_no }}')">
                    <div>
                        <div class="rack-title-section">
                            <div class="rack-title">
                                <i class="fas fa-chevron-down rack-toggle"></i>
                                <i class="fas fa-warehouse me-2"></i>
                                Rack {{ rack_no }}
                            </div>
                        </div>
                        
                        <div class="rack-stats">
                            <div class="rack-stat">
                                <div class="rack-stat-number">{{ rack_data.total_bundles }}</div>
                                <div class="rack-stat-label">Bundles</div>
                            </div>
                            <div class="rack-stat">
                                <div class="rack-stat-number">{{ rack_data.total_files }}</div>
                                <div class="rack-stat-label">Files</div>
                            </div>
                            <div class="rack-stat">
                                {% set unique_villages = rack_data.bundles.values() | map(attribute='village_name') | unique | list %}
                                <div class="rack-stat-number">{{ unique_villages | length }}</div>
                                <div class="rack-stat-label">Villages</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bundles-container">
                    <div class="bundles-grid">
                        {% for bundle_key, bundle_data in rack_data.bundles.items() %}
                        <div class="bundle-card" data-bundle="{{ bundle_key }}" onclick="viewBundle('{{ rack_no }}', '{{ bundle_data.bundle_number }}', '{{ bundle_data.village_name }}')">
                            <div class="bundle-header">
                                <div class="bundle-title">{{ bundle_data.bundle_display_name }}</div>
                                <div class="bundle-file-count">{{ bundle_data.file_count }} files</div>
                            </div>

                            <div class="bundle-village">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ bundle_data.village_name }}</span>
                            </div>

                            {% if bundle_data.categories %}
                            <div class="bundle-categories">
                                {% for category, count in bundle_data.categories.items() %}
                                <div class="category-tag">{{ category }} ({{ count }})</div>
                                {% endfor %}
                            </div>
                            {% endif %}

                            <div class="bundle-actions">
                                <a href="/bundles/rack/{{ rack_no }}/bundle/{{ bundle_data.bundle_number }}/village/{{ bundle_data.village_name | urlencode }}" class="btn-bundle-action" onclick="event.stopPropagation()">
                                    <i class="fas fa-eye me-1"></i>View Files
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <i class="fas fa-sitemap"></i>
                <h3>No Bundle Data Found</h3>
                <p>Upload Excel files with rack and bundle information to see hierarchical organization.</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
    // Rack toggle functionality
    function toggleRack(rackNo) {
        const rackSection = document.querySelector(`[data-rack="${rackNo}"]`);
        rackSection.classList.toggle('collapsed');
        
        // Save collapsed state to localStorage
        const collapsedRacks = JSON.parse(localStorage.getItem('collapsedRacks') || '[]');
        if (rackSection.classList.contains('collapsed')) {
            if (!collapsedRacks.includes(rackNo)) {
                collapsedRacks.push(rackNo);
            }
        } else {
            const index = collapsedRacks.indexOf(rackNo);
            if (index > -1) {
                collapsedRacks.splice(index, 1);
            }
        }
        localStorage.setItem('collapsedRacks', JSON.stringify(collapsedRacks));
    }
    
    // Bundle view functionality
    function viewBundle(rackNo, bundleNo, villageName) {
        window.location.href = `/bundles/rack/${rackNo}/bundle/${bundleNo}/village/${encodeURIComponent(villageName)}`;
    }
    
    // Filter racks
    function filterRacks() {
        const searchTerm = document.getElementById('rackSearch').value.toLowerCase();
        const rackSections = document.querySelectorAll('.rack-section');
        
        rackSections.forEach(section => {
            const rackNo = section.dataset.rack.toLowerCase();
            if (rackNo.includes(searchTerm)) {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        });
    }
    
    // Filter bundles
    function filterBundles() {
        const searchTerm = document.getElementById('bundleSearch').value.toLowerCase();
        const bundleCards = document.querySelectorAll('.bundle-card');
        
        bundleCards.forEach(card => {
            const bundleTitle = card.querySelector('.bundle-title').textContent.toLowerCase();
            const villageText = card.querySelector('.bundle-village span').textContent.toLowerCase();
            
            if (bundleTitle.includes(searchTerm) || villageText.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    
    // Restore collapsed state on page load
    document.addEventListener('DOMContentLoaded', function() {
        const collapsedRacks = JSON.parse(localStorage.getItem('collapsedRacks') || '[]');
        collapsedRacks.forEach(rackNo => {
            const rackSection = document.querySelector(`[data-rack="${rackNo}"]`);
            if (rackSection) {
                rackSection.classList.add('collapsed');
            }
        });
    });
</script>
{% endblock %}
