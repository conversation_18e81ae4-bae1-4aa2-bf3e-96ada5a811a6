from models.file import File
from models.location import Location
from models.access_log import AccessLog
from app import db
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

def get_file_access_history(file_id):
    """
    Get the access history for a specific file.
    
    Args:
        file_id (int): The ID of the file
        
    Returns:
        list: A list of access log entries
    """
    return AccessLog.query.filter_by(file_id=file_id).order_by(AccessLog.timestamp.desc()).all()

def get_file_location(file_id):
    """
    Get the location information for a specific file.
    
    Args:
        file_id (int): The ID of the file
        
    Returns:
        dict: The location information
    """
    file = File.query.get(file_id)
    if file and file.location:
        return {
            'rack': file.location.rack_number,
            'row': file.location.row_number,
            'position': file.location.position
        }
    return None

def get_access_frequency(days=30):
    """
    Get the access frequency of files over the specified number of days.
    
    Args:
        days (int): The number of days to look back
        
    Returns:
        dict: A dictionary mapping file IDs to access counts
    """
    cutoff_date = datetime.utcnow() - timedelta(days=days)
    
    # Query for access logs within the time period
    logs = AccessLog.query.filter(AccessLog.timestamp >= cutoff_date).all()
    
    # Count accesses by file
    file_counts = {}
    for log in logs:
        file_counts[log.file_id] = file_counts.get(log.file_id, 0) + 1
    
    return file_counts

def suggest_optimal_locations():
    """
    Suggest optimal file locations based on access patterns.
    
    Returns:
        list: A list of suggested relocations
    """
    # Get access frequency data
    access_data = get_access_frequency(days=90)  # Look at the last 90 days
    
    # Get all files and their current locations
    files = File.query.all()
    
    # Create a DataFrame for analysis
    data = []
    for file in files:
        data.append({
            'file_id': file.id,
            'title': file.title,
            'rack': file.location.rack_number if file.location else None,
            'row': file.location.row_number if file.location else None,
            'position': file.location.position if file.location else None,
            'access_count': access_data.get(file.id, 0)
        })
    
    df = pd.DataFrame(data)
    
    # Skip if not enough data
    if len(df) < 10:
        return []
    
    # Sort by access count (descending)
    df = df.sort_values('access_count', ascending=False)
    
    # Identify files that should be moved to more accessible locations
    high_access_files = df.head(int(len(df) * 0.2))  # Top 20% most accessed files
    low_access_files = df.tail(int(len(df) * 0.2))   # Bottom 20% least accessed files
    
    # Generate suggestions
    suggestions = []
    
    # For simplicity, we'll just suggest moving high-access files to lower rack numbers
    # and low-access files to higher rack numbers
    for _, file in high_access_files.iterrows():
        if file['rack'] and int(file['rack']) > 2:  # If rack number is high
            suggestions.append({
                'file_id': file['file_id'],
                'title': file['title'],
                'current_location': f"Rack: {file['rack']}, Row: {file['row']}, Position: {file['position']}",
                'suggested_location': f"Rack: 1, Row: {file['row']}, Position: {file['position']}",
                'reason': f"High access frequency ({file['access_count']} accesses)"
            })
    
    for _, file in low_access_files.iterrows():
        if file['rack'] and int(file['rack']) < 3:  # If rack number is low
            suggestions.append({
                'file_id': file['file_id'],
                'title': file['title'],
                'current_location': f"Rack: {file['rack']}, Row: {file['row']}, Position: {file['position']}",
                'suggested_location': f"Rack: 5, Row: {file['row']}, Position: {file['position']}",
                'reason': f"Low access frequency ({file['access_count']} accesses)"
            })
    
    return suggestions

# Add to imports
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

def predict_related_files(file_id):
    """
    Predict files that are likely to be accessed together with the given file
    using machine learning.
    
    Args:
        file_id (int): The ID of the file
        
    Returns:
        list: A list of related file IDs
    """
    # Get all access logs
    logs = AccessLog.query.all()
    
    # Create a user-file matrix
    user_file_data = {}
    for log in logs:
        if log.user_id not in user_file_data:
            user_file_data[log.user_id] = {}
        if log.file_id not in user_file_data[log.user_id]:
            user_file_data[log.user_id][log.file_id] = 0
        user_file_data[log.user_id][log.file_id] += 1
    
    # Convert to a matrix format suitable for ML
    file_ids = list(set([log.file_id for log in logs]))
    user_ids = list(set([log.user_id for log in logs if log.user_id is not None]))
    
    # Skip if not enough data
    if len(file_ids) < 5 or len(user_ids) < 3:
        return []
    
    # Create feature matrix
    X = np.zeros((len(file_ids), len(user_ids)))
    file_id_to_index = {file_id: i for i, file_id in enumerate(file_ids)}
    user_id_to_index = {user_id: i for i, user_id in enumerate(user_ids)}
    
    for user_id, files in user_file_data.items():
        if user_id is None or user_id not in user_id_to_index:
            continue
        for file_id, count in files.items():
            if file_id in file_id_to_index:
                X[file_id_to_index[file_id]][user_id_to_index[user_id]] = count
    
    # Normalize the data
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Apply KMeans clustering
    kmeans = KMeans(n_clusters=min(5, len(file_ids)), random_state=42)
    clusters = kmeans.fit_predict(X_scaled)
    
    # Find the cluster of the target file
    if file_id not in file_id_to_index:
        return []
    
    target_file_cluster = clusters[file_id_to_index[file_id]]
    
    # Get files in the same cluster
    related_file_indices = [i for i, cluster in enumerate(clusters) if cluster == target_file_cluster and file_ids[i] != file_id]
    related_file_ids = [file_ids[i] for i in related_file_indices]
    
    return related_file_ids[:5]  # Return top 5 related files