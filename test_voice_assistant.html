<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Assistant Test - T-Office</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="static/css/main.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
        }
        .voice-demo {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
        }
        .command-example {
            background: white;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-search-container {
            position: relative;
            margin: 20px 0;
        }
        .test-search-input {
            width: 100%;
            padding: 12px 50px 12px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
        }
        .test-voice-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: #007bff;
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-microphone me-3"></i>
            Voice Assistant Test
        </h1>
        
        <div class="voice-demo">
            <h3><i class="fas fa-search me-2"></i>Test Voice Search</h3>
            <div class="test-search-container">
                <input type="text" class="test-search-input" placeholder="Search files or ask voice assistant..." id="globalSearch">
                <button class="test-voice-btn" type="button" id="voiceSearchBtn" title="Voice Assistant (Ctrl+M)">
                    <i class="fas fa-microphone"></i>
                </button>
            </div>
            
            <div class="mt-4">
                <h5>Try these voice commands:</h5>
                
                <div class="command-example">
                    <strong>Search Commands:</strong><br>
                    "Search for survey 123"<br>
                    "Find village records"<br>
                    "Look for file number ABC123"
                </div>
                
                <div class="command-example">
                    <strong>Navigation Commands:</strong><br>
                    "Open dashboard"<br>
                    "Go to bundles"<br>
                    "Show analytics"<br>
                    "Scan QR code"
                </div>
                
                <div class="command-example">
                    <strong>Question Commands:</strong><br>
                    "Where is the search option?"<br>
                    "How do I search files?"<br>
                    "Where are the bundles?"<br>
                    "What commands are available?"
                </div>
                
                <div class="command-example">
                    <strong>Control Commands:</strong><br>
                    "Help" - Show all commands<br>
                    "Stop" - Stop speaking/listening
                </div>
            </div>
            
            <div class="alert alert-info mt-4">
                <i class="fas fa-lightbulb me-2"></i>
                <strong>Tips:</strong>
                <ul class="mb-0 mt-2">
                    <li>Click the microphone button or press <kbd>Ctrl+M</kbd> to start voice search</li>
                    <li>The button will turn red when listening and green when speaking</li>
                    <li>You can ask natural questions like "Where is the search option?"</li>
                    <li>The assistant will provide voice responses to guide you</li>
                    <li>Click the button again to stop listening or speaking</li>
                </ul>
            </div>
        </div>
        
        <div class="text-center">
            <button class="btn btn-primary btn-lg" onclick="window.location.href='/dashboard'">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/main.js"></script>
    <script src="static/js/voice_search.js"></script>
    
    <script>
        // Mock TOffice object for testing
        window.TOffice = {
            showNotification: function(message, type, duration) {
                console.log(`Notification [${type}]: ${message}`);
                // Create a simple notification
                const notification = document.createElement('div');
                notification.className = `alert alert-${type === 'danger' ? 'danger' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info'} position-fixed`;
                notification.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
                notification.innerHTML = `<i class="fas fa-info-circle me-2"></i>${message}`;
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.remove();
                }, duration || 3000);
            },
            trackEvent: function(event, data) {
                console.log(`Event tracked: ${event}`, data);
            }
        };
        
        // Mock search API for testing
        window.fetch = function(url) {
            if (url.includes('/api/search')) {
                return Promise.resolve({
                    json: () => Promise.resolve({
                        results: [
                            {
                                id: 1,
                                title: "Sample Document 1",
                                description: "Test document for voice search",
                                location: { rack: 1, row: 2, position: 3 }
                            },
                            {
                                id: 2,
                                title: "Sample Document 2", 
                                description: "Another test document",
                                location: { rack: 2, row: 1, position: 5 }
                            }
                        ]
                    })
                });
            }
            return Promise.reject('Not found');
        };
    </script>
</body>
</html>
